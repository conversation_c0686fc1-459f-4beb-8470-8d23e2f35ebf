{"__meta": {"id": "01K1W4RNC9MXHC23FYP4YR5H5Y", "datetime": "2025-08-05 04:35:25", "utime": **********.322372, "method": "GET", "uri": "/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.107706, "end": **********.336638, "duration": 0.22893190383911133, "duration_str": "229ms", "measures": [{"label": "Booting", "start": **********.107706, "relative_start": 0, "end": **********.302224, "relative_end": **********.302224, "duration": 0.****************, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.302236, "relative_start": 0.*****************, "end": **********.336641, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "34.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.313251, "relative_start": 0.*****************, "end": **********.317723, "relative_end": **********.317723, "duration": 0.004472017288208008, "duration_str": "4.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.320684, "relative_start": 0.*****************, "end": **********.320814, "relative_end": **********.320814, "duration": 0.00012993812561035156, "duration_str": "130μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.320829, "relative_start": 0.*****************, "end": **********.320842, "relative_end": **********.320842, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "229ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-145850307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-145850307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-771305539 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-771305539\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2046262580 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkFENXZCU2FPbDdQbms4dnFmMGtVV2c9PSIsInZhbHVlIjoiTVFSOHBac0RBeVF0R2hRQnlmSit5TUtGZUtGVmRFVzkzeGNIWFhRaWQwUUJ6Q2NvVk45SERmbVpTM1FGZUsvUzNRVUhaaENwNWxhQXdDNlBtMUxPVnJZcHRtL1RpWXVyOThNRjZvcUpxcm1vM1h2aWFRSzNVWnVxYllaOEUvbWsiLCJtYWMiOiI3NDE4NjdiMTE3NjlmNGMzZjhlMjJlMzAxYzJiYjYzNDI4YzEyYTRiYzA0ZTZkZDdjODZkZGNjOWM0OWMyMjkxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkZtUzNYY09oRFlQbUJoUmFKWHkyZ3c9PSIsInZhbHVlIjoiL3RDZ295Q3E0TWplN2Z6eHRwbmZ5eGR5dEFoNjZIZFRhUzgxVVBlRUFGVHFwTmkxMTR4TXMxZEZGTVZrTVFsdkw2N3lGSnBQNEpmb0pPUUxmQm9IeEJlc1FoNGFzeGlheEpqS3N4TllUWEFKa3BaQ3FHNnBVOFU4NXFjaU5rUTIiLCJtYWMiOiJlYTM5YWY4MTc5MjlhNzk5MWYyYjcwNmMwZTliMGIyMzQ4MDljY2IzOTMwYzU4YmM1MzNiMzlkZDIyMWQ3ZTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mlk.test/1022-iphone-ip16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046262580\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-615860637 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkFENXZCU2FPbDdQbms4dnFmMGtVV2c9PSIsInZhbHVlIjoiTVFSOHBac0RBeVF0R2hRQnlmSit5TUtGZUtGVmRFVzkzeGNIWFhRaWQwUUJ6Q2NvVk45SERmbVpTM1FGZUsvUzNRVUhaaENwNWxhQXdDNlBtMUxPVnJZcHRtL1RpWXVyOThNRjZvcUpxcm1vM1h2aWFRSzNVWnVxYllaOEUvbWsiLCJtYWMiOiI3NDE4NjdiMTE3NjlmNGMzZjhlMjJlMzAxYzJiYjYzNDI4YzEyYTRiYzA0ZTZkZDdjODZkZGNjOWM0OWMyMjkxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZtUzNYY09oRFlQbUJoUmFKWHkyZ3c9PSIsInZhbHVlIjoiL3RDZ295Q3E0TWplN2Z6eHRwbmZ5eGR5dEFoNjZIZFRhUzgxVVBlRUFGVHFwTmkxMTR4TXMxZEZGTVZrTVFsdkw2N3lGSnBQNEpmb0pPUUxmQm9IeEJlc1FoNGFzeGlheEpqS3N4TllUWEFKa3BaQ3FHNnBVOFU4NXFjaU5rUTIiLCJtYWMiOiJlYTM5YWY4MTc5MjlhNzk5MWYyYjcwNmMwZTliMGIyMzQ4MDljY2IzOTMwYzU4YmM1MzNiMzlkZDIyMWQ3ZTc0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615860637\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-105987584 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">32732</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">a4717e300cc331221e231ba8dbfdfa56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:35:25 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105987584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1676094186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1676094186\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/195/cVWuEHD6PY8c2lFOjW5slnxVDq51yYuAYYOHkoEP.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}