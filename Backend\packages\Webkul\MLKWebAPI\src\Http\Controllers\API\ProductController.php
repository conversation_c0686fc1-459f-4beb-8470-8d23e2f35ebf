<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\Request;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Product\Repositories\ProductCustomerGroupPriceRepository;
use Webkul\Product\Repositories\ProductAttributeValueRepository;
use Webkul\Customer\Repositories\CustomerRepository;
use Webkul\Customer\Repositories\CustomerGroupRepository;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Attribute\Repositories\AttributeOptionRepository;
use Webkul\MLKWebAPI\Http\Resources\ProductResource;
use Webkul\Core\Jobs\UpdateCreateVisitIndex;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Webkul\Theme\Repositories\ThemeCustomizationRepository;

class ProductController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Product\Repositories\ProductRepository  $productRepository
     * @param  \Webkul\Product\Repositories\ProductCustomerGroupPriceRepository  $productCustomerGroupPriceRepository
     * @param  \Webkul\Product\Repositories\ProductAttributeValueRepository  $productAttributeValueRepository
     * @param  \Webkul\Customer\Repositories\CustomerRepository  $customerRepository
     * @param  \Webkul\Customer\Repositories\CustomerGroupRepository  $customerGroupRepository
     * @param  \Webkul\Attribute\Repositories\AttributeRepository  $attributeRepository
     * @param  \Webkul\Attribute\Repositories\AttributeOptionRepository  $attributeOptionRepository
     * @param  \Webkul\Theme\Repositories\ThemeCustomizationRepository  $themeCustomizationRepository
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository,
        protected ProductCustomerGroupPriceRepository $productCustomerGroupPriceRepository,
        protected ProductAttributeValueRepository $productAttributeValueRepository,
        protected CustomerRepository $customerRepository,
        protected CustomerGroupRepository $customerGroupRepository,
        protected AttributeRepository $attributeRepository,
        protected AttributeOptionRepository $attributeOptionRepository,
        protected ThemeCustomizationRepository $themeCustomizationRepository
    )
    {
    }

    /**
     * Get product detail by id or url_key.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail(Request $request)
    {
        // 验证输入参数 - 支持id或url_key，但至少需要其中一个
        $request->validate([
            'id' => 'nullable|integer',
            'url_key' => 'nullable|string',
        ]);
        
        // 确保至少提供了id或url_key中的一个
        if (!$request->has('id') && !$request->has('url_key')) {
            return $this->error('Either id or url_key parameter is required', 422);
        }

        // 如果同时提供了id和url_key，优先使用id（保持向后兼容性）
        if ($request->has('id') && $request->has('url_key')) {
            return $this->error('Please provide either id or url_key parameter, not both', 422);
        }
        
        try {
            // 获取当前客户组 - 使用系统方法确保正确性
            $customerGroup = $this->customerRepository->getCurrentGroup();
            $currentChannel = core()->getCurrentChannel();
            
            // 根据参数类型查找产品
            if ($request->has('id')) {
                $id = $request->get('id');
                // 使用ID查找产品，预加载核心关联，并过滤价格索引
                $product = $this->productRepository->with([
                    'price_indices' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->where('channel_id', $currentChannel->id);
                    },
                    'categories',
                    'images',
                    'videos', 
                    'attribute_family',
                    //'attribute_values', // 预加载自定义属性值
                    'inventory_indices' => function ($query) use ($currentChannel) {
                        $query->where('channel_id', $currentChannel->id);
                    },
                    'customer_group_prices' => function ($query) use ($customerGroup) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->orderBy('qty', 'asc');
                    },
                    'catalog_rule_prices' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->where('channel_id', $currentChannel->id)
                              ->where('rule_date', now()->format('Y-m-d'));
                    },
                    'variants' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->with([
                            'images',
                            //'attribute_values', // 预加载自定义属性值
                            'price_indices' => function ($q) use ($customerGroup, $currentChannel) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->where('channel_id', $currentChannel->id);
                            },
                            'inventory_indices' => function ($q) use ($currentChannel) {
                                $q->where('channel_id', $currentChannel->id);
                            },
                            'customer_group_prices' => function ($q) use ($customerGroup) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->orderBy('qty', 'asc');
                            },
                            'catalog_rule_prices' => function ($q) use ($customerGroup, $currentChannel) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->where('channel_id', $currentChannel->id)
                                  ->where('rule_date', now()->format('Y-m-d'));
                            }
                        ]);
                    },
                    'customizable_options.customizable_option_prices',
                    'downloadable_samples',
                    'downloadable_links',
                    'bundle_options.bundle_option_products.product',
                    'booking_products',
                    'channels'
                ])->findOrFail($id);
            } else {
                // 使用url_key查找产品
                $urlKey = $request->get('url_key');
                
                // 先尝试使用findBySlug方法查找产品
                $foundProduct = $this->productRepository->findBySlug($urlKey);
                
                if (!$foundProduct) {
                    return $this->notFound('Product not found with the provided url_key');
                }
                
                // 使用找到的产品ID重新查询并预加载相关数据
                $product = $this->productRepository->with([
                    'price_indices' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->where('channel_id', $currentChannel->id);
                    },
                    'categories',
                    'images',
                    'videos', 
                    'attribute_family',
                    //'attribute_values', // 预加载自定义属性值
                    'inventory_indices' => function ($query) use ($currentChannel) {
                        $query->where('channel_id', $currentChannel->id);
                    },
                    'customer_group_prices' => function ($query) use ($customerGroup) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->orderBy('qty', 'asc');
                    },
                    'catalog_rule_prices' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->where('customer_group_id', $customerGroup->id)
                              ->where('channel_id', $currentChannel->id)
                              ->where('rule_date', now()->format('Y-m-d'));
                    },
                    'variants' => function ($query) use ($customerGroup, $currentChannel) {
                        $query->with([
                            'images',
                            //'attribute_values', // 预加载自定义属性值
                            'price_indices' => function ($q) use ($customerGroup, $currentChannel) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->where('channel_id', $currentChannel->id);
                            },
                            'inventory_indices' => function ($q) use ($currentChannel) {
                                $q->where('channel_id', $currentChannel->id);
                            },
                            'customer_group_prices' => function ($q) use ($customerGroup) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->orderBy('qty', 'asc');
                            },
                            'catalog_rule_prices' => function ($q) use ($customerGroup, $currentChannel) {
                                $q->where('customer_group_id', $customerGroup->id)
                                  ->where('channel_id', $currentChannel->id)
                                  ->where('rule_date', now()->format('Y-m-d'));
                            }
                        ]);
                    },
                    'customizable_options.customizable_option_prices',
                    'downloadable_samples',
                    'downloadable_links',
                    'bundle_options.bundle_option_products.product',
                    'booking_products',
                    'channels'
                ])->findOrFail($foundProduct->id);
            }

            // 检查产品是否可见和有效
            if (
                ! $product->url_key
                || ! $product->visible_individually
                || ! $product->status
            ) {
                return $this->notFound();
            }

            // 获取产品类型实例
            $productTypeInstance = $product->getTypeInstance();

            // 准备基础数据，使用正确的价格计算方法
            $data = [
                'product' => new ProductResource($product),
                'type' => $product->type ?? 'simple',
            ];

            // 安全地获取类型实例方法，确保使用当前客户组的价格
            try {
                $data['is_saleable'] = $productTypeInstance->isSaleable();
                $data['is_stockable'] = $productTypeInstance->isStockable();
                $data['total_quantity'] = $productTypeInstance->totalQuantity();
                $data['show_quantity_box'] = $productTypeInstance->showQuantityBox();
                
                // 获取基于当前客户组的价格信息
                $data['prices'] = [
                    'regular' => [
                        'price' => $product->price,
                        'formatted_price' => core()->currency($product->price),
                    ],
                    'final' => [
                        'price' => $productTypeInstance->getMinimalPrice(),
                        'formatted_price' => core()->currency($productTypeInstance->getMinimalPrice()),
                    ],
                    'maximum' => [
                        'price' => $productTypeInstance->getMaximumPrice(),
                        'formatted_price' => core()->currency($productTypeInstance->getMaximumPrice()),
                    ]
                ];
                
                // 检查是否有折扣
                $data['have_discount'] = $productTypeInstance->haveDiscount();
                
            } catch (\Exception $e) {
                logger()->error('Error getting product type instance methods: ' . $e->getMessage());
                $data['is_saleable'] = false;
                $data['is_stockable'] = false;
                $data['total_quantity'] = 0;
                $data['show_quantity_box'] = false;
                $data['prices'] = [
                    'regular' => ['price' => 0, 'formatted_price' => core()->currency(0)],
                    'final' => ['price' => 0, 'formatted_price' => core()->currency(0)],
                    'maximum' => ['price' => 0, 'formatted_price' => core()->currency(0)]
                ];
                $data['have_discount'] = false;
            }

            // 添加当前客户组信息
            $data['customer_group'] = [
                'id' => $customerGroup->id,
                'name' => $customerGroup->name,
                'code' => $customerGroup->code,
                'is_user_defined' => $customerGroup->is_user_defined ?? false,
            ];

            // 添加当前渠道信息
            $data['channel'] = [
                'id' => $currentChannel->id,
                'code' => $currentChannel->code,
                'name' => $currentChannel->name,
            ];

            // 添加变体产品信息（针对可配置产品），包含客户组特定价格
            if ($product->type === 'configurable' && $product->variants->isNotEmpty()) {
                $data['variants'] = $product->variants->map(function ($variant) use ($customerGroup) {
                    try {
                        $variantTypeInstance = $variant->getTypeInstance();
                        
                        return [
                            'id' => $variant->id,
                            'sku' => $variant->sku ?? '',
                            'name' => $variant->name ?? '',
                            'price' => $variant->price ?? 0,
                            'formatted_price' => core()->currency($variant->price ?? 0),
                            'final_price' => $variantTypeInstance->getMinimalPrice(),
                            'formatted_final_price' => core()->currency($variantTypeInstance->getMinimalPrice()),
                            'special_price' => $variant->special_price,
                            'have_discount' => $variantTypeInstance->haveDiscount(),
                            'color' => $this->getVariantColorInfo($variant),
                            'images' => $variant->images ? $variant->images->map(function ($image) {
                                return [
                                    'id' => $image->id,
                                    'url' => $image->url ?? '',
                                    'path' => $image->path ?? '',
                                    'position' => $image->position ?? 0,
                                ];
                            }) : collect(),
                            'inventory' => [
                                'qty' => $variantTypeInstance->totalQuantity(),
                                'is_in_stock' => $variantTypeInstance->haveSufficientQuantity(1),
                            ],
                            'customer_group_prices' => $this->getCustomerGroupPricesForProduct($variant, $customerGroup),
                            'specifications' => $this->getProductSpecifications($variant),
                        ];
                    } catch (\Exception $e) {
                        logger()->error('Error processing variant: ' . $e->getMessage());
                        return null;
                    }
                })->filter();
            }

            // 添加可定制选项（针对简单产品）
            if ($product->customizable_options && $product->customizable_options->isNotEmpty()) {
                $data['customizable_options'] = $product->customizable_options->map(function ($option) {
                    try {
                        return [
                            'id' => $option->id,
                            'type' => $option->type ?? '',
                            'label' => $option->label ?? '',
                            'is_required' => (bool)($option->is_required ?? false),
                            'sort_order' => $option->sort_order ?? 0,
                            'prices' => $option->customizable_option_prices ? $option->customizable_option_prices->map(function ($price) {
                                try {
                                    return [
                                        'id' => $price->id,
                                        'label' => $price->label ?? '',
                                        'price' => $price->price ?? 0,
                                        'formatted_price' => core()->currency($price->price ?? 0),
                                        'price_type' => $price->price_type ?? 'fixed',
                                        'sort_order' => $price->sort_order ?? 0,
                                    ];
                                } catch (\Exception $e) {
                                    logger()->error('Error processing customizable option price: ' . $e->getMessage());
                                    return null;
                                }
                            })->filter() : collect(),
                        ];
                    } catch (\Exception $e) {
                        logger()->error('Error processing customizable option: ' . $e->getMessage());
                        return null;
                    }
                })->filter();
            }

            // 添加下载链接和样本（针对可下载产品）
            if ($product->type === 'downloadable') {
                if ($product->downloadable_links && $product->downloadable_links->isNotEmpty()) {
                    $data['downloadable_links'] = $product->downloadable_links->map(function ($link) {
                        try {
                            return [
                                'id' => $link->id,
                                'title' => $link->title ?? '',
                                'price' => $link->price ?? 0,
                                'formatted_price' => core()->currency($link->price ?? 0),
                                'downloads' => $link->downloads ?? 0,
                                'sample_url' => $link->sample_url ?? '',
                                'sample_file_url' => $link->sample_file_url ?? '',
                                'sort_order' => $link->sort_order ?? 0,
                            ];
                        } catch (\Exception $e) {
                            logger()->error('Error processing downloadable link: ' . $e->getMessage());
                            return null;
                        }
                    })->filter();
                }

                if ($product->downloadable_samples && $product->downloadable_samples->isNotEmpty()) {
                    $data['downloadable_samples'] = $product->downloadable_samples->map(function ($sample) {
                        try {
                            return [
                                'id' => $sample->id,
                                'title' => $sample->title ?? '',
                                'url' => $sample->url ?? '',
                                'file_url' => $sample->file_url ?? '',
                                'sort_order' => $sample->sort_order ?? 0,
                            ];
                        } catch (\Exception $e) {
                            logger()->error('Error processing downloadable sample: ' . $e->getMessage());
                            return null;
                        }
                    })->filter();
                }
            }

            // 添加套装选项（针对套装产品）
            if ($product->type === 'bundle' && $product->bundle_options && $product->bundle_options->isNotEmpty()) {
                $data['bundle_options'] = $product->bundle_options->map(function ($option) {
                    try {
                        return [
                            'id' => $option->id,
                            'type' => $option->type ?? '',
                            'label' => $option->label ?? '',
                            'is_required' => (bool)($option->is_required ?? false),
                            'sort_order' => $option->sort_order ?? 0,
                            'products' => $option->bundle_option_products ? $option->bundle_option_products->map(function ($optionProduct) {
                                try {
                                    return [
                                        'id' => $optionProduct->id,
                                        'product_id' => $optionProduct->product_id ?? 0,
                                        'product_name' => $optionProduct->product->name ?? '',
                                        'qty' => $optionProduct->qty ?? 0,
                                        'is_default' => (bool)($optionProduct->is_default ?? false),
                                        'is_user_defined' => (bool)($optionProduct->is_user_defined ?? false),
                                        'sort_order' => $optionProduct->sort_order ?? 0,
                                    ];
                                } catch (\Exception $e) {
                                    logger()->error('Error processing bundle option product: ' . $e->getMessage());
                                    return null;
                                }
                            })->filter() : collect(),
                        ];
                    } catch (\Exception $e) {
                        logger()->error('Error processing bundle option: ' . $e->getMessage());
                        return null;
                    }
                })->filter();
            }

            // 添加客户组特定价格信息
            $data['customer_group_prices'] = $this->getCustomerGroupPricesForProduct($product, $customerGroup);

            // 添加批发用户价格信息 (customer_group_id == 3)
            $data['wholesale_prices'] = $this->getWholesalePricesForProduct($product);

            // 添加产品规格信息
            $data['specifications'] = $this->getProductSpecifications($product);

            // 添加产品的SEO信息
            $data['seo'] = [
                'meta_title' => $product->meta_title,
                'meta_description' => $product->meta_description,
                'meta_keywords' => $product->meta_keywords,
                'url_key' => $product->url_key,
            ];

            // 添加特价信息，考虑客户组价格
            try {
                $specialPriceInfo = $this->getSpecialPriceInfo($product, $productTypeInstance);
                if ($specialPriceInfo) {
                    $data['special_price'] = $specialPriceInfo;
                }
            } catch (\Exception $e) {
                logger()->error('Error getting special price information: ' . $e->getMessage());
            }

            // 添加设备和品牌信息
            try {
                $deviceBrandInfo = $this->getDeviceBrandInfo($product);
                if (!empty($deviceBrandInfo['devices'])) {
                    $data['devices'] = $deviceBrandInfo['devices'];
                }
                if (!empty($deviceBrandInfo['brands'])) {
                    $data['brands'] = $deviceBrandInfo['brands'];
                }
                // 添加品牌-设备-子产品id关联信息（用于前端联动筛选）
                if (!empty($deviceBrandInfo['brand_device_variants'])) {
                    $data['brand_device_variants'] = $deviceBrandInfo['brand_device_variants'];
                }
            } catch (\Exception $e) {
                logger()->error('Error getting device/brand information: ' . $e->getMessage());
            }

            // 添加相同group_code产品的设备品牌颜色关联数据
            try {
                $groupDeviceVariants = $this->getGroupDeviceVariants($product);
                if (!empty($groupDeviceVariants)) {
                    $data['group_device_variants'] = $groupDeviceVariants;
                }
            } catch (\Exception $e) {
                logger()->error('Error getting group device variants: ' . $e->getMessage());
            }

            // 添加产品横幅
            //$data['product_banners'] = $this->getThemeCustomization('product_banner');
            // 将banner图片转换为url
            if (isset($data['product']['scene']) && !empty($data['product']['scene'])) {
                foreach ($data['product']['scene'] as $key => $bannerImage) {
                    $data['product_banners']['images'][$key]['image'] = Storage::url($bannerImage);
                }
            }else{
                $data['product_banners']['images'] = [];
            }

            return $this->success($data);
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFound();
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }



    /**
     * Get related products.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function relatedProducts($id)
    {
        try {
            $product = $this->productRepository->findOrFail($id);

            $relatedProducts = $product->related_products()
                ->take(core()->getConfigData('catalog.products.product_view_page.no_of_related_products'))
                ->get();

            return $this->success(
                ProductResource::collection($relatedProducts)
            );
        } catch (\Exception $e) {
            return $this->notFound();
        }
    }

    /**
     * Get up-sell products.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function upSellProducts($id)
    {
        try {
            $product = $this->productRepository->findOrFail($id);
            
            $upSellProducts = $product->up_sells()
                ->take(core()->getConfigData('catalog.products.product_view_page.no_of_up_sells_products'))
                ->get();
            
            return $this->success(
                ProductResource::collection($upSellProducts)
            );
        } catch (\Exception $e) {
            return $this->notFound();
        }
    }

    /**
     * Get cross-sell products.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function crossSellProducts($id)
    {
        try {
            $product = $this->productRepository->findOrFail($id);

            $crossSellProducts = $product->cross_sells()
                ->take(core()->getConfigData('catalog.products.product_view_page.no_of_cross_sells_products') ?? 4)
                ->get();
            
            return $this->success(
                ProductResource::collection($crossSellProducts)
            );
        } catch (\Exception $e) {
            return $this->notFound();
        }
    }

    /**
     * Record product visit for statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recordVisit(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer',
        ]);
        
        try {
            // 获取产品
            $product = $this->productRepository->findOrFail($request->product_id);
            
            // 检查产品是否可见
            if (
                ! $product->url_key
                || ! $product->visible_individually
                || ! $product->status
            ) {
                return $this->notFound("Product not found");
            }
            
            // 准备访问日志
            $log = [
                'method'       => $request->method(),
                'url'          => $request->fullUrl(),
                'referer'      => $request->header('referer'),
                'ip'           => $request->ip(),
                'visitor_id'   => $request->user()?->id,
                'visitor_type' => $request->user() ? get_class($request->user()) : null,
                'channel_id'   => core()->getCurrentChannel()->id,
                'path_info'    => $request->getPathInfo(),
                'useragent'    => $request->userAgent(),
                'headers'      => json_encode($request->headers->all()),
            ];
            
            // 分发任务记录访问
            UpdateCreateVisitIndex::dispatch($product, $log);
            
            return $this->success(
                ['recorded' => true],
                "Product visit recorded successfully"
            );
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFound("Product not found");
        } catch (\Exception $e) {
            return $this->error(
                "Product visit recorded failed",
                400,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Check if special price is currently active.
     *
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return bool
     */
    private function isSpecialPriceActive($startDate, $endDate)
    {
        $now = now();
        
        // 如果没有开始时间，认为特价立即生效
        $isAfterStart = true;
        if ($startDate) {
            $isAfterStart = $now->greaterThanOrEqualTo($startDate);
        }
        
        // 如果没有结束时间，认为特价永久有效
        $isBeforeEnd = true;
        if ($endDate) {
            $isBeforeEnd = $now->lessThanOrEqualTo($endDate);
        }
        
        return $isAfterStart && $isBeforeEnd;
    }



        /**
     * 获取主题自定义
     *
     * @param string $themeName
     * @return array
     */
    protected function getThemeCustomization($themeName)
    {

        // 获取当前语言
        $locale = core()->getRequestedLocaleCode();
        // 获取当前渠道ID
        $channelId = core()->getCurrentChannel()->id;

        // 查询主题自定义表
        $themeCustomization = $this->themeCustomizationRepository->findWhere([
            'name' => $themeName,
            'channel_id' => $channelId,
            'status' => 1
        ])->first();

        if (!$themeCustomization) {
            return [];
        }
       
        // 获取当前语言的翻译
        $translation = $themeCustomization->translate($locale);
        // 如果当前语言没有翻译，则使用zh_CN翻译
        if (!$translation) {
            $translation = $themeCustomization->translate('en');
        } 
        if (!isset($translation->options)) {
            return [];
        }
        return $translation->options;
    }

    /**
     * 获取变体产品的颜色属性信息
     *
     * @param  \Webkul\Product\Models\Product  $variant
     * @return array|null
     */
    protected function getVariantColorInfo($variant)
    {
        try {
            // 获取颜色属性值
            $colorValue = $variant->color;
            
            if (!$colorValue) {
                return null;
            }
            
            // 如果是多选值，取第一个（通常颜色是单选）
            $colorId = is_numeric($colorValue) ? $colorValue : explode(',', $colorValue)[0];
            
            if (!$colorId) {
                return null;
            }
            
            // 获取颜色属性选项信息
            $colorOption = $this->attributeOptionRepository->find($colorId);
            
            if (!$colorOption) {
                return null;
            }
            
            return [
                'id' => $colorOption->id,
                'label' => $colorOption->label ?? $colorOption->admin_name,
                'admin_name' => $colorOption->admin_name,
                'swatch_value' => $colorOption->swatch_value,
                'sort_order' => $colorOption->sort_order ?? 0,
            ];
            
        } catch (\Exception $e) {
            logger()->error('Error getting variant color info: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取客户组特定价格信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @param  \Webkul\Customer\Models\CustomerGroup  $customerGroup
     * @return array
     */
    protected function getCustomerGroupPricesForProduct($product, $customerGroup)
    {
        $prices = [];
        $productTypeInstance = $product->getTypeInstance();

        if ($product->customer_group_prices && $product->customer_group_prices->isNotEmpty()) {
            foreach ($product->customer_group_prices as $groupPrice) {
                try {
                    $priceValue = is_object($groupPrice) ? ($groupPrice->value ?? 0) : 0;
                    $productPrice = $product->price ?? 0;

                    if (is_object($groupPrice) && ($groupPrice->value_type ?? '') == 'discount') {
                        $priceValue = $productPrice - ($productPrice * $priceValue / 100);
                    }

                    $prices[] = [
                        'qty' => is_object($groupPrice) ? ($groupPrice->qty ?? 0) : 0,
                        'value_type' => is_object($groupPrice) ? ($groupPrice->value_type ?? 'fixed') : 'fixed',
                        'original_value' => is_object($groupPrice) ? ($groupPrice->value ?? 0) : 0,
                        'price' => $priceValue,
                        'formatted_price' => core()->currency($priceValue),
                    ];
                } catch (\Exception $e) {
                    logger()->error('Error processing customer group price: ' . $e->getMessage());
                    continue;
                }
            }
        }

                 // 添加目录规则价格（如果有）
         if ($product->catalog_rule_prices && $product->catalog_rule_prices->isNotEmpty()) {
             foreach ($product->catalog_rule_prices as $rulePrice) {
                 try {
                     $priceValue = is_object($rulePrice) ? ($rulePrice->price ?? 0) : 0;

                     $prices[] = [
                         'qty' => 1, // 目录规则通常应用于任何数量
                         'value_type' => 'catalog_rule',
                         'original_value' => $priceValue,
                         'price' => $priceValue,
                         'formatted_price' => core()->currency($priceValue),
                         'rule_id' => is_object($rulePrice) ? ($rulePrice->catalog_rule_id ?? 0) : 0,
                         'rule_date' => is_object($rulePrice) ? ($rulePrice->rule_date ?? null) : null,
                     ];
                 } catch (\Exception $e) {
                     logger()->error('Error processing catalog rule price: ' . $e->getMessage());
                     continue;
                 }
             }
         }

                 // 添加基础价格（如果没有其他价格规则）
         if (empty($prices)) {
             $prices[] = [
                 'qty' => 1,
                 'value_type' => 'regular',
                 'original_value' => $product->price ?? 0,
                 'price' => $product->price ?? 0,
                 'formatted_price' => core()->currency($product->price ?? 0),
             ];
         }

         // 按数量排序，确保价格层级正确
         usort($prices, function ($a, $b) {
             if ($a['qty'] === $b['qty']) {
                 // 同数量时，优先级：catalog_rule > customer_group > regular
                 $priority = ['catalog_rule' => 1, 'discount' => 2, 'fixed' => 3, 'regular' => 4];
                 return ($priority[$a['value_type']] ?? 5) - ($priority[$b['value_type']] ?? 5);
             }
             return $a['qty'] - $b['qty'];
         });

         return $prices;
    }

    /**
     * 获取特价信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @param  \Webkul\Product\Models\ProductType  $productTypeInstance
     * @return array|null
     */
    protected function getSpecialPriceInfo($product, $productTypeInstance)
    {
        try {
            $specialPrice = $product->special_price;
            $regularPrice = $product->price;
            $specialPriceFrom = $product->special_price_from;
            $specialPriceTo = $product->special_price_to;
            
            if ($specialPrice && $regularPrice && $specialPrice > 0 && $regularPrice > 0) {
                // 计算折扣百分比
                $discountPercentage = round((($regularPrice - $specialPrice) / $regularPrice) * 100, 2);
                
                return [
                    'price' => $specialPrice,
                    'formatted_price' => core()->currency($specialPrice),
                    'regular_price' => $regularPrice,
                    'formatted_regular_price' => core()->currency($regularPrice),
                    'discount_percentage' => $discountPercentage,
                    'start_date' => $specialPriceFrom ? date('Y-m-d H:i:s', strtotime($specialPriceFrom)) : null,
                    'end_date' => $specialPriceTo ? date('Y-m-d H:i:s', strtotime($specialPriceTo)) : null,
                    'start_date_formatted' => $specialPriceFrom ? date('Y-m-d', strtotime($specialPriceFrom)) : null,
                    'end_date_formatted' => $specialPriceTo ? date('Y-m-d', strtotime($specialPriceTo)) : null,
                    'is_active' => $this->isSpecialPriceActive($specialPriceFrom, $specialPriceTo),
                ];
            }
            return null;
        } catch (\Exception $e) {
            logger()->error('Error getting special price information: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取设备和品牌信息，支持configurable产品的联动筛选
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getDeviceBrandInfo($product)
    {
        try {
            // 对于configurable产品，从子产品中获取关联信息
            if ($product->type === 'configurable' && $product->variants->isNotEmpty()) {
                return $this->getConfigurableDeviceBrandInfo($product);
            }

            // 对于simple产品，使用原有逻辑
            return $this->getSimpleDeviceBrandInfo($product);

        } catch (\Exception $e) {
            logger()->error('Error getting device/brand information: ' . $e->getMessage());
            return [
                'devices' => [],
                'brands' => [],
                'brand_device_variants' => [], // 品牌-设备-子产品id关联
            ];
        }
    }

    /**
     * 获取configurable产品的设备品牌信息，包含子产品id关联
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getConfigurableDeviceBrandInfo($product)
    {
        $devices = [];
        $brands = [];
        $brandDeviceVariants = []; // 品牌-设备-子产品id关联数组

        // 预加载变体的属性值
        $variants = $product->variants()->with('attribute_values')->get();

        foreach ($variants as $variant) {
            // 获取变体的brand、device属性
            $brandValue = $variant->brand;
            $deviceValue = $variant->device;

            // 处理brand属性
            $brandIds = [];
            if ($brandValue) {
                $brandIds = is_numeric($brandValue) ? [$brandValue] : array_filter(explode(',', $brandValue));
            }

            // 处理device属性
            $deviceIds = [];
            if ($deviceValue) {
                $deviceIds = array_filter(explode(',', $deviceValue));
            }

            // 建立品牌-设备-子产品id关联
            foreach ($brandIds as $brandId) {
                foreach ($deviceIds as $deviceId) {
                    // 初始化品牌数组
                    if (!isset($brandDeviceVariants[$brandId])) {
                        $brandDeviceVariants[$brandId] = [];
                    }

                    // 初始化设备数组
                    if (!isset($brandDeviceVariants[$brandId][$deviceId])) {
                        $brandDeviceVariants[$brandId][$deviceId] = [];
                    }

                    // 添加子产品id到关联中（避免重复）
                    if (!in_array($variant->id, $brandDeviceVariants[$brandId][$deviceId])) {
                        $brandDeviceVariants[$brandId][$deviceId][] = $variant->id;
                    }
                }
            }
        }

        // 获取所有唯一的brand、device IDs
        $allBrandIds = array_keys($brandDeviceVariants);
        $allDeviceIds = [];

        foreach ($brandDeviceVariants as $brandId => $deviceVariants) {
            foreach ($deviceVariants as $deviceId => $variantIds) {
                $allDeviceIds[] = $deviceId;
            }
        }

        $allDeviceIds = array_unique($allDeviceIds);

        // 获取品牌选项信息
        if (!empty($allBrandIds)) {
            $brandOptions = $this->attributeOptionRepository->findWhereIn('id', $allBrandIds);
            foreach ($brandOptions as $brandOption) {
                $brands[] = [
                    'id' => $brandOption->id,
                    'name' => $brandOption->admin_name ?? $brandOption->label,
                    'sort_order' => $brandOption->sort_order ?? 0,
                ];
            }
        }

        // 获取设备选项信息
        if (!empty($allDeviceIds)) {
            $deviceOptions = $this->attributeOptionRepository->findWhereIn('id', $allDeviceIds);
            foreach ($deviceOptions as $deviceOption) {
                $devices[] = [
                    'id' => $deviceOption->id,
                    'name' => $deviceOption->admin_name ?? $deviceOption->label,
                    'sort_order' => $deviceOption->sort_order ?? 0,
                    'parent_id' => $deviceOption->parent_id ?? 0,
                ];
            }
        }

        return [
            'devices' => $devices,
            'brands' => $brands,
            'brand_device_variants' => $brandDeviceVariants, // 品牌-设备-子产品id关联
        ];
    }

    /**
     * 获取simple产品的设备品牌信息（原有逻辑）
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getSimpleDeviceBrandInfo($product)
    {
        $devices = [];
        $brands = [];

        $deviceValue = $product->device;
        if ($deviceValue) {
            $deviceIds = array_filter(explode(',', $deviceValue));
            if (!empty($deviceIds)) {
                $deviceOptions = $this->attributeOptionRepository->findWhereIn('id', $deviceIds);
                foreach ($deviceOptions as $deviceOption) {
                    $devices[] = [
                        'id' => $deviceOption->id,
                        'name' => $deviceOption->admin_name ?? $deviceOption->label,
                        'sort_order' => $deviceOption->sort_order ?? 0,
                        'parent_id' => $deviceOption->parent_id ?? 0,
                    ];
                    if ($deviceOption->parent_id) {
                        $brands[] = $deviceOption->parent_id;
                    }
                }
            }
        }

        // 获取品牌信息（去重后）
        if (!empty($brands)) {
            $brandIds = array_unique($brands);
            $brandOptions = $this->attributeOptionRepository->findWhereIn('id', $brandIds);
            $brands = [];
            foreach ($brandOptions as $brandOption) {
                $brands[] = [
                    'id' => $brandOption->id,
                    'name' => $brandOption->admin_name ?? $brandOption->label,
                    'sort_order' => $brandOption->sort_order ?? 0,
                ];
            }
        } else {
            // 如果没有通过设备获取到品牌，直接获取品牌属性
            $brandValue = $product->brand;
            if ($brandValue) {
                $brandIds = is_numeric($brandValue) ? [$brandValue] : array_filter(explode(',', $brandValue));
                if (!empty($brandIds)) {
                    $brandOptions = $this->attributeOptionRepository->findWhereIn('id', $brandIds);
                    foreach ($brandOptions as $brandOption) {
                        $brands[] = [
                            'id' => $brandOption->id,
                            'name' => $brandOption->admin_name ?? $brandOption->label,
                            'sort_order' => $brandOption->sort_order ?? 0,
                        ];
                    }
                }
            }
        }

        return [
            'devices' => $devices,
            'brands' => $brands,
            'brand_device_variants' => [], // simple产品不需要关联数据
        ];
    }

    /**
     * 获取批发用户价格信息 (customer_group_id == 3)
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getWholesalePricesForProduct($product)
    {
        $wholesalePrices = [];

        try {
            // 查询批发用户的价格记录 (customer_group_id == 3)
            $wholesaleGroupPrices = $this->productCustomerGroupPriceRepository->findWhere([
                'product_id' => $product->id,
                'customer_group_id' => 3
            ]);

            foreach ($wholesaleGroupPrices as $groupPrice) {
                try {
                    $priceValue = $groupPrice->value ?? 0;
                    $productPrice = $product->price ?? 0;

                    // 如果是折扣类型，计算实际价格
                    if (($groupPrice->value_type ?? '') == 'discount') {
                        $priceValue = $productPrice - ($productPrice * $priceValue / 100);
                    }

                    $wholesalePrices[] = [
                        'id' => $groupPrice->id,
                        'customer_group_id' => $groupPrice->customer_group_id,
                        'qty' => $groupPrice->qty ?? 0,
                        'value_type' => $groupPrice->value_type ?? 'fixed',
                        'original_value' => $groupPrice->value ?? 0,
                        'price' => $priceValue,
                        'formatted_price' => core()->currency($priceValue),
                        'unique_id' => $groupPrice->unique_id ?? '',
                        'created_at' => $groupPrice->created_at ? $groupPrice->created_at->format('Y-m-d H:i:s') : null,
                        'updated_at' => $groupPrice->updated_at ? $groupPrice->updated_at->format('Y-m-d H:i:s') : null,
                        "qty_format" => trans('mlk::app.products.qty-range', ['qty' => $groupPrice->qty]),
                    ];
                } catch (\Exception $e) {
                    logger()->error('Error processing wholesale price: ' . $e->getMessage());
                    continue;
                }
            }

            // 按数量排序
            usort($wholesalePrices, function ($a, $b) {
                return $a['qty'] - $b['qty'];
            });

            return $wholesalePrices;
        } catch (\Exception $e) {
            logger()->error('Error getting wholesale prices: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取产品规格信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getProductSpecifications($product)
    {
        try {
            $specifications = [];
            // 获取重量信息
            $weight = $product->weight ?? null;
            if ($weight !== null && $weight > 0) {
                $specifications['weight'] = [
                    'value' => (float) $weight,
                    'unit' => 'kg', // 或根据系统配置获取单位
                    'formatted' => $weight . ' kg',
                ];
            }
            $specifications['dimensions'] = [];
            // 获取长度信息
            $length = $product->length ?? null;
            if ($length !== null && $length > 0) {
                $specifications['dimensions']['length'] = [
                    'value' => (float) $length,
                    'unit' => 'cm', // 或根据系统配置获取单位
                    'formatted' => $length . ' cm',
                ];
            }
            

            // 获取宽度信息
            $width = $product->width ?? null;
            if ($width !== null && $width > 0) {
                $specifications['dimensions']['width'] = [
                    'value' => (float) $width,
                    'unit' => 'cm', // 或根据系统配置获取单位
                    'formatted' => $width . ' cm',
                ];
   
            }
            
            // 获取高度信息
            $height = $product->height ?? null;
            if ($height !== null && $height > 0) {
                $specifications['dimensions']['height'] = [
                    'value' => (float) $height,
                    'unit' => 'cm', // 或根据系统配置获取单位
                    'formatted' => $height . ' cm',
                ];
            }

            if (isset($product->profile) && $product->profile != "") {
                $specifications['profile'] = [
                    'value' => $product->profile,
                    'unit' => 'level',
                    'formatted' => 'level ' . $product->profile,
                ];
            }

            if (isset($product->DROP_rating) && $product->DROP_rating != "") {
                $dropRating = $this->attributeOptionRepository->find($product->DROP_rating);
                $specifications['drop_rating'] = [
                    'value' => $dropRating->admin_name,
                    'unit' => 'level',
                    'formatted' => 'level ' . $dropRating->admin_name,
                ];
            }


            return $specifications;
            
        } catch (\Exception $e) {
            logger()->error('Error getting product specifications: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取属性选项名称
     *
     * @param  string|int  $value
     * @return array
     */
    protected function getAttributeOptionNames($value)
    {
        try {
            $optionIds = is_numeric($value) ? [$value] : array_filter(explode(',', $value));
            if (empty($optionIds)) {
                return [];
            }

            $options = $this->attributeOptionRepository->findWhereIn('id', $optionIds);
            return $options->pluck('admin_name')->filter()->toArray();

        } catch (\Exception $e) {
            logger()->error('Error getting attribute option names: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取相同group_code产品的设备品牌颜色关联数据
     * 当group_code为空或没有关联产品时，返回当前产品的属性信息
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array
     */
    protected function getGroupDeviceVariants($product)
    {
        try {
            // 获取当前产品的group_code值
            $groupCode = $product->group_code;

            $groupVariants = [];

            if (!empty($groupCode)) {
                // 获取相同group_code的所有产品的brand、device、color属性值
                $groupVariants = $this->getProductsByGroupCodeWithAttributes($groupCode);
            }

            // 如果group_code为空或没有找到关联产品，返回当前产品的属性信息
            if (empty($groupVariants)) {
                $currentProductAttributes = $this->getCurrentProductAttributes($product);
                if (!empty($currentProductAttributes)) {
                    $groupVariants = [$currentProductAttributes];
                }
            }

            // 构建产品关联数组，包含brand-device映射关系
            $result = [];
            foreach ($groupVariants as $variant) {
                $result[] = [
                    'product_id' => $variant['product_id'],
                    'brands' => $variant['brands'] ?? [],
                    'devices' => $variant['devices'] ?? [],
                    'brand_device_mapping' => $variant['brand_device_mapping'] ?? [],
                    'color' => $variant['color'],
                ];
            }

            return $result;

        } catch (\Exception $e) {
            logger()->error('Error getting group device variants: ' . $e->getMessage());
            // 发生异常时，尝试返回当前产品的属性信息
            try {
                $currentProductAttributes = $this->getCurrentProductAttributes($product);
                if (!empty($currentProductAttributes)) {
                    return [[
                        'product_id' => $currentProductAttributes['product_id'],
                        'brands' => $currentProductAttributes['brands'] ?? [],
                        'devices' => $currentProductAttributes['devices'] ?? [],
                        'brand_device_mapping' => $currentProductAttributes['brand_device_mapping'] ?? [],
                        'color' => $currentProductAttributes['color'],
                    ]];
                }
            } catch (\Exception $fallbackException) {
                logger()->error('Error getting current product attributes as fallback: ' . $fallbackException->getMessage());
            }
            return [];
        }
    }

    /**
     * 高效查询相同group_code的产品及其brand、device、color属性值
     * 处理brand多值和brand-device映射关系
     *
     * @param  string  $groupCode
     * @return array
     */
    protected function getProductsByGroupCodeWithAttributes($groupCode)
    {
        try {
            // 获取属性ID，使用缓存避免重复查询
            $attributes = $this->getAttributeIds(['group_code', 'brand', 'device', 'color']);

            if (empty($attributes)) {
                return [];
            }

            // 构建高效的JOIN查询
            $query = DB::table('products as p')
                ->select([
                    'p.id as product_id',
                    'brand_val.text_value as brand',
                    'device_val.text_value as device',
                    'color_val.integer_value as color'
                ])
                // JOIN group_code属性值表
                ->join('product_attribute_values as group_val', function ($join) use ($attributes) {
                    $join->on('p.id', '=', 'group_val.product_id')
                         ->where('group_val.attribute_id', $attributes['group_code']);
                })
                // LEFT JOIN brand属性值表 (brand可能是多选，存储在text_value中)
                ->leftJoin('product_attribute_values as brand_val', function ($join) use ($attributes) {
                    $join->on('p.id', '=', 'brand_val.product_id')
                         ->where('brand_val.attribute_id', $attributes['brand']);
                })
                // LEFT JOIN device属性值表
                ->leftJoin('product_attribute_values as device_val', function ($join) use ($attributes) {
                    $join->on('p.id', '=', 'device_val.product_id')
                         ->where('device_val.attribute_id', $attributes['device']);
                })
                // LEFT JOIN color属性值表
                ->leftJoin('product_attribute_values as color_val', function ($join) use ($attributes) {
                    $join->on('p.id', '=', 'color_val.product_id')
                         ->where('color_val.attribute_id', $attributes['color']);
                })
                // 过滤相同的group_code
                ->where('group_val.text_value', $groupCode)
                // 只查询有效产品
                ->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                          ->from('product_attribute_values as status_val')
                          ->join('attributes as status_attr', 'status_val.attribute_id', '=', 'status_attr.id')
                          ->whereRaw('status_val.product_id = p.id')
                          ->where('status_attr.code', 'status')
                          ->where('status_val.boolean_value', 1);
                });

            $results = $query->get()->toArray();

            // 处理结果，解析brand和device的关联关系
            return $this->processProductAttributesWithMapping($results);

        } catch (\Exception $e) {
            logger()->error('Error querying products by group code: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 处理产品属性数据，解析brand-device映射关系
     *
     * @param  array  $rawResults
     * @return array
     */
    protected function processProductAttributesWithMapping($rawResults)
    {
        try {
            $processedResults = [];

            foreach ($rawResults as $item) {
                // 解析brand值（可能是多个，逗号分隔）
                $brandIds = !empty($item->brand) ? array_filter(explode(',', $item->brand)) : [];

                // 解析device值（可能是多个，逗号分隔）
                $deviceIds = !empty($item->device) ? array_filter(explode(',', $item->device)) : [];

                // 获取brand-device映射关系
                $brandDeviceMapping = $this->getBrandDeviceMapping($brandIds, $deviceIds);

                $processedResults[] = [
                    'product_id' => $item->product_id,
                    'brands' => $brandDeviceMapping['brands'],
                    'devices' => $brandDeviceMapping['devices'],
                    'brand_device_mapping' => $brandDeviceMapping['mapping'],
                    'color' => $item->color,
                ];
            }

            return $processedResults;

        } catch (\Exception $e) {
            logger()->error('Error processing product attributes with mapping: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取brand-device映射关系
     *
     * @param  array  $brandIds
     * @param  array  $deviceIds
     * @return array
     */
    protected function getBrandDeviceMapping($brandIds, $deviceIds)
    {
        try {
            $brands = [];
            $devices = [];
            $mapping = [];

            if (empty($brandIds) && empty($deviceIds)) {
                return ['brands' => $brands, 'devices' => $devices, 'mapping' => $mapping];
            }

            // 获取所有相关的属性选项信息
            $allOptionIds = array_merge($brandIds, $deviceIds);
            $options = $this->attributeOptionRepository->with(['parent'])
                ->findWhereIn('id', $allOptionIds);

            // 收集所有相关的brand ID（包括通过device的parent_id关联的）
            $allBrandIds = array_merge($brandIds, []);

            // 处理device信息并收集parent_id作为brand
            foreach ($deviceIds as $deviceId) {
                $deviceOption = $options->where('id', $deviceId)->first();
                if ($deviceOption) {
                    $device = [
                        'id' => $deviceOption->id,
                        'name' => $deviceOption->admin_name ?? $deviceOption->label,
                        'sort_order' => $deviceOption->sort_order ?? 0,
                        'parent_id' => $deviceOption->parent_id,
                    ];

                    $devices[] = $device;

                    // 建立brand-device映射关系
                    if ($deviceOption->parent_id) {
                        if (!isset($mapping[$deviceOption->parent_id])) {
                            $mapping[$deviceOption->parent_id] = [];
                        }
                        $mapping[$deviceOption->parent_id][] = $deviceOption->id;

                        // 将device的parent_id添加到brand列表中
                        if (!in_array($deviceOption->parent_id, $allBrandIds)) {
                            $allBrandIds[] = $deviceOption->parent_id;
                        }
                    }
                }
            }

            // 如果有新的brand ID需要查询，重新获取选项信息
            if (count($allBrandIds) > count($brandIds)) {
                $additionalBrandIds = array_diff($allBrandIds, $brandIds);
                if (!empty($additionalBrandIds)) {
                    $additionalOptions = $this->attributeOptionRepository
                        ->findWhereIn('id', $additionalBrandIds);
                    $options = $options->merge($additionalOptions);
                }
            }

            // 处理所有brand信息
            foreach ($allBrandIds as $brandId) {
                $brandOption = $options->where('id', $brandId)->first();
                if ($brandOption) {
                    $brands[] = [
                        'id' => $brandOption->id,
                        'name' => $brandOption->admin_name ?? $brandOption->label,
                        'sort_order' => $brandOption->sort_order ?? 0,
                    ];
                }
            }

            // 按sort_order排序
            usort($brands, function($a, $b) {
                return $a['sort_order'] - $b['sort_order'];
            });

            usort($devices, function($a, $b) {
                return $a['sort_order'] - $b['sort_order'];
            });

            return [
                'brands' => $brands,
                'devices' => $devices,
                'mapping' => $mapping
            ];

        } catch (\Exception $e) {
            logger()->error('Error getting brand device mapping: ' . $e->getMessage());
            return ['brands' => [], 'devices' => [], 'mapping' => []];
        }
    }

    /**
     * 获取当前产品的brand、device、color属性值
     *
     * @param  \Webkul\Product\Models\Product  $product
     * @return array|null
     */
    protected function getCurrentProductAttributes($product)
    {
        try {
            // 直接从产品模型获取属性值（利用Product模型的getAttribute魔术方法）
            $brandValue = $product->brand ?? null;
            $deviceValue = $product->device ?? null;
            $colorValue = $product->color ?? null;

            // 解析brand和device值
            $brandIds = !empty($brandValue) ? array_filter(explode(',', $brandValue)) : [];
            $deviceIds = !empty($deviceValue) ? array_filter(explode(',', $deviceValue)) : [];

            // 获取brand-device映射关系
            $brandDeviceMapping = $this->getBrandDeviceMapping($brandIds, $deviceIds);

            return [
                'product_id' => $product->id,
                'brands' => $brandDeviceMapping['brands'],
                'devices' => $brandDeviceMapping['devices'],
                'brand_device_mapping' => $brandDeviceMapping['mapping'],
                'color' => $colorValue,
            ];

        } catch (\Exception $e) {
            logger()->error('Error getting current product attributes: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取属性ID映射，使用缓存提高性能
     *
     * @param  array  $attributeCodes
     * @return array
     */
    protected function getAttributeIds(array $attributeCodes)
    {
        static $attributeCache = [];

        $result = [];
        $needQuery = [];

        // 检查缓存
        foreach ($attributeCodes as $code) {
            if (isset($attributeCache[$code])) {
                $result[$code] = $attributeCache[$code];
            } else {
                $needQuery[] = $code;
            }
        }

        // 查询未缓存的属性
        if (!empty($needQuery)) {
            $attributes = $this->attributeRepository->findWhereIn('code', $needQuery, ['id', 'code']);

            foreach ($attributes as $attribute) {
                $attributeCache[$attribute->code] = $attribute->id;
                $result[$attribute->code] = $attribute->id;
            }
        }

        return $result;
    }
}