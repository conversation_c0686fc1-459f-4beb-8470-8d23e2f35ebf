{"__meta": {"id": "01K1W4N939AY4ZQNTW48746B6R", "datetime": "2025-08-05 04:33:34", "utime": **********.442337, "method": "GET", "uri": "/cache/original/product/222/cABsLc42tCl4uEFneNVKEurAGaK1SP27oR43ayL6.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.24644, "end": **********.455074, "duration": 0.2086341381072998, "duration_str": "209ms", "measures": [{"label": "Booting", "start": **********.24644, "relative_start": 0, "end": **********.421062, "relative_end": **********.421062, "duration": 0.****************, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.421072, "relative_start": 0.*****************, "end": **********.455077, "relative_end": 2.86102294921875e-06, "duration": 0.034004926681518555, "duration_str": "34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.432606, "relative_start": 0.*****************, "end": **********.436631, "relative_end": **********.436631, "duration": 0.004024982452392578, "duration_str": "4.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.440845, "relative_start": 0.*****************, "end": **********.440948, "relative_end": **********.440948, "duration": 0.000102996826171875, "duration_str": "103μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.44096, "relative_start": 0.****************, "end": **********.440972, "relative_end": **********.440972, "duration": 1.2159347534179688e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/222/cABsLc42tCl4uEFneNVKEurAGaK1SP27oR43ayL6.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "209ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1047673948 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1047673948\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1463916203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1463916203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1332045180 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImZWeWJqb1I2cWdoRHBpUW9ZVzNpa2c9PSIsInZhbHVlIjoiV01WTU50ZU02eFpkazFIOC8veDVJVlRabjF2a1NVMDZEYVpuWnBGN2VwZkgwbkZhVTZkV0JOaURIM1ArWEZJMXRCeFI3cytNdGg2bXkxNDhKbzFkdWRrU3Y3aUE3VUR4WXpZbHpVWjhwd1A0Z0FEc0hrb251K0dyektWZXNoeGgiLCJtYWMiOiJmZmJlNjhlMzMxMDk0Yzg0ZmFhMjA3NDg3N2Q2ZDI2MzE2YjY3MGQ5MWMyOGMyY2M4YTZlZDk1N2U2ZjllMDExIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImYrdFFja080Rjd5bFUwRC9qRDluQ2c9PSIsInZhbHVlIjoiSWJOckpvai9yMkh2d285cnJ3MGVTRDZCaXVtckxlVFlFSGhrOW4vM0pRWE1qN3dwYWM2U1NKWU9zRWxaOVdUNGgrMzFmMGFweE85Rm5LajhjTytjaUdIbDVEeXZNYk9yZjNhaGlHd1ZLMDZGTzViOTA5RzhFRm9GR0ZhQzlKeXkiLCJtYWMiOiI0ZTZmZTA0YjYzN2MxNTdhNzI2ZWEzZThhMDI4MDFiMTY4ZDllYzM0MDk3ZjQwNGQ4MjJlN2I5ZDhhNjI3Yjc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://mlk.test/4125-iphone-ip17-pro?locale=de</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332045180\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1810160693 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImZWeWJqb1I2cWdoRHBpUW9ZVzNpa2c9PSIsInZhbHVlIjoiV01WTU50ZU02eFpkazFIOC8veDVJVlRabjF2a1NVMDZEYVpuWnBGN2VwZkgwbkZhVTZkV0JOaURIM1ArWEZJMXRCeFI3cytNdGg2bXkxNDhKbzFkdWRrU3Y3aUE3VUR4WXpZbHpVWjhwd1A0Z0FEc0hrb251K0dyektWZXNoeGgiLCJtYWMiOiJmZmJlNjhlMzMxMDk0Yzg0ZmFhMjA3NDg3N2Q2ZDI2MzE2YjY3MGQ5MWMyOGMyY2M4YTZlZDk1N2U2ZjllMDExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImYrdFFja080Rjd5bFUwRC9qRDluQ2c9PSIsInZhbHVlIjoiSWJOckpvai9yMkh2d285cnJ3MGVTRDZCaXVtckxlVFlFSGhrOW4vM0pRWE1qN3dwYWM2U1NKWU9zRWxaOVdUNGgrMzFmMGFweE85Rm5LajhjTytjaUdIbDVEeXZNYk9yZjNhaGlHd1ZLMDZGTzViOTA5RzhFRm9GR0ZhQzlKeXkiLCJtYWMiOiI0ZTZmZTA0YjYzN2MxNTdhNzI2ZWEzZThhMDI4MDFiMTY4ZDllYzM0MDk3ZjQwNGQ4MjJlN2I5ZDhhNjI3Yjc2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810160693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1180210657 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">39210</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">4b2c163e649aeb4b4594b446c80ac6e6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:33:34 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180210657\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1743485691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1743485691\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/222/cABsLc42tCl4uEFneNVKEurAGaK1SP27oR43ayL6.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}