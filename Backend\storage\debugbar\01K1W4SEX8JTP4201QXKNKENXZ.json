{"__meta": {"id": "01K1W4SEX8JTP4201QXKNKENXZ", "datetime": "2025-08-05 04:35:51", "utime": **********.464467, "method": "GET", "uri": "/cache/original/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.276862, "end": **********.473708, "duration": 0.19684600830078125, "duration_str": "197ms", "measures": [{"label": "Booting", "start": **********.276862, "relative_start": 0, "end": **********.446733, "relative_end": **********.446733, "duration": 0.*****************, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.446744, "relative_start": 0.*****************, "end": **********.47371, "relative_end": 2.1457672119140625e-06, "duration": 0.026966094970703125, "duration_str": "26.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.457411, "relative_start": 0.*****************, "end": **********.460744, "relative_end": **********.460744, "duration": 0.003332853317260742, "duration_str": "3.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.463108, "relative_start": 0.*****************, "end": **********.463212, "relative_end": **********.463212, "duration": 0.00010395050048828125, "duration_str": "104μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.463224, "relative_start": 0.*****************, "end": **********.463237, "relative_end": **********.463237, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "197ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-130589667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-130589667\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-678980775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-678980775\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-435634678 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkU4bTZYRUpxcVVGOFVjb1FsRE1rSEE9PSIsInZhbHVlIjoidWRXb2YrWXg3R0JzZTcyQ05iTUFBUkV4dE9ZTU1LTVIvZDcvYytaRkwrZVRpbmFtTkZVUWtDa25FampreEM0MWUzNE5HL0dWY2J6Q2c3N3NUeHBPaE1MRC9sUlk1RXY4VW9zNDVLSVJpWE9POE1mY2NKRWFYWi9tYlYrNm1yY0EiLCJtYWMiOiI5MDk0ZTAyZWY3NWVlYWE3ODc2Y2I5ZGQzODkzYzdiOGU4OTgyMDhkZWIzYWYxZjBjMTEwMzcwZWQ3MDgxMzUxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImIxdDdBeSs1ejlFWUhlZTV5bVJVR0E9PSIsInZhbHVlIjoiSk02Y2ljb3JFMkdkNFJ5M01oaW9WaWZLamdzOVBxVmdLRXE3MjBGeWpGNkRDZlNBUWF1NXFpalpQQ2VuWU9TeXExZjRONkkwK1piKy9mRkQxRmt6ek9BUTlrUElUcVZNWDh5OFpRTmZUUzZ6RlhTWTNSK1FtZlRndUx0VUZ6TksiLCJtYWMiOiJlYTBmNDYwMjEyOGZhZGNlZDhkOGQ0MWFhMTllYjQwNDllZWM0ZGQ5MTkyMjM5ODlhOWIxYWMyYThkMDFmNThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://mlk.test/SKU-CS-011-variant-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435634678\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1722797231 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkU4bTZYRUpxcVVGOFVjb1FsRE1rSEE9PSIsInZhbHVlIjoidWRXb2YrWXg3R0JzZTcyQ05iTUFBUkV4dE9ZTU1LTVIvZDcvYytaRkwrZVRpbmFtTkZVUWtDa25FampreEM0MWUzNE5HL0dWY2J6Q2c3N3NUeHBPaE1MRC9sUlk1RXY4VW9zNDVLSVJpWE9POE1mY2NKRWFYWi9tYlYrNm1yY0EiLCJtYWMiOiI5MDk0ZTAyZWY3NWVlYWE3ODc2Y2I5ZGQzODkzYzdiOGU4OTgyMDhkZWIzYWYxZjBjMTEwMzcwZWQ3MDgxMzUxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImIxdDdBeSs1ejlFWUhlZTV5bVJVR0E9PSIsInZhbHVlIjoiSk02Y2ljb3JFMkdkNFJ5M01oaW9WaWZLamdzOVBxVmdLRXE3MjBGeWpGNkRDZlNBUWF1NXFpalpQQ2VuWU9TeXExZjRONkkwK1piKy9mRkQxRmt6ek9BUTlrUElUcVZNWDh5OFpRTmZUUzZ6RlhTWTNSK1FtZlRndUx0VUZ6TksiLCJtYWMiOiJlYTBmNDYwMjEyOGZhZGNlZDhkOGQ0MWFhMTllYjQwNDllZWM0ZGQ5MTkyMjM5ODlhOWIxYWMyYThkMDFmNThkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722797231\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-818807651 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">19584</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">c775d9f2b67bcc1e3b3aebe7901410bb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:35:51 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818807651\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2031195765 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2031195765\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/173/QRhKER5J3wapj1forTYRhVEHhFKjeyXhtktAZeVU.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}