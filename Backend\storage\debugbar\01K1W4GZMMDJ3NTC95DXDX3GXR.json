{"__meta": {"id": "01K1W4GZMMDJ3NTC95DXDX3GXR", "datetime": "2025-08-05 04:31:13", "utime": **********.684504, "method": "PUT", "uri": "/admin/catalog/products/edit/219?channel=default&locale=fr", "ip": "127.0.0.1"}, "modules": {"count": 6, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (128)", "Webkul\\Attribute\\Models\\AttributeFamily (10)"], "views": [], "queries": [{"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 219", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 1.69, "duration_str": "1.69s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock')", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 3.51, "duration_str": "3.51s", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": [], "views": [], "queries": [{"sql": "select exists(select 1 from `category_translations` where `slug` = '4125-iphone-ip17-pro' limit 1) as `exists`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (14)", "Webkul\\Core\\Models\\CoreConfig (3)", "Webkul\\Core\\Models\\Locale (81)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.92, "duration_str": "1.92s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.attribute.file_attribute_upload_size'", "duration": 1.88, "duration_str": "1.88s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.81, "duration_str": "1.81s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 1.86, "duration_str": "1.86s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.auto_translation.enabled' and `channel_code` = 'default'", "duration": 1.89, "duration_str": "1.89s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 3.19, "duration_str": "3.19s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 1.68, "duration_str": "1.68s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 220", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 221", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 222", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 223", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 224", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 225", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}, {"name": "Webkul\\Marketing", "models": [], "views": [], "queries": [{"sql": "select * from `url_rewrites` where `entity_type` in ('category', 'product') and `request_path` = '4125-iphone-ip17-pro'", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (47)", "Webkul\\Product\\Models\\ProductAttributeValue (388)", "Webkul\\Product\\Models\\ProductImage (16)", "Webkul\\Product\\Models\\ProductInventory (6)", "Webkul\\Product\\Models\\ProductFlat (35)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = '219' limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id` from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `products` where `sku` = '8034412526218' and `id` <> '219'", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = '4125-iphone-ip17-pro'", "duration": 2.27, "duration_str": "2.27s", "connection": "mlk"}, {"sql": "select count(`id`) as aggregate from `product_attribute_values` where `text_value` = '4125 IP17 PRO' and `attribute_id` = 27 and `product_id` != '219'", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 219 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 219 and `product_attribute_values`.`product_id` is not null", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4793 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4822 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `text_value` = '<p>D&eacute;couvrez l\\'iPhone 17 Pro, une fusion remarquable de technologie de pointe et de design &eacute;l&eacute;gant. Dot&eacute; d\\'un &eacute;cran Super Retina XDR &eacute;poustouflant, l\\'iPhone 17 Pro offre des couleurs vives et des d&eacute;tails incroyables, rendant chaque image et vid&eacute;o vivante. &Eacute;quip&eacute; de la derni&egrave;re puce A17 Bionic, cet appareil offre des performances et une efficacit&eacute; fulgurantes, garantissant un multit&acirc;che fluide et des exp&eacute;riences de jeu immersives.<br><br>L\\'iPhone 17 Pro dispose d\\'un syst&egrave;me de triple cam&eacute;ra avanc&eacute;, comprenant un objectif principal de 48 MP, ainsi que des objectifs ultra grand angle et t&eacute;l&eacute;objectif, vous permettant de capturer des photos et des vid&eacute;os &eacute;poustouflantes dans toutes les conditions d\\'&eacute;clairage. Avec un mode nuit am&eacute;lior&eacute; et des capacit&eacute;s ProRAW, votre cr&eacute;ativit&eacute; ne conna&icirc;t pas de limites. L\\'appareil prend &eacute;galement en charge la connectivit&eacute; 5G, offrant des vitesses Internet ultrarapides pour le streaming, le jeu et la navigation.<br><br>Con&ccedil;u avec la durabilit&eacute; &agrave; l\\'esprit, l\\'iPhone 17 Pro dispose d\\'un verre Ceramic Shield &agrave; l\\'avant et d\\'un cadre en acier inoxydable &eacute;l&eacute;gant, le rendant &agrave; la fois styl&eacute; et r&eacute;sistant. Avec iOS 17, profitez d\\'une exp&eacute;rience plus personnalis&eacute;e gr&acirc;ce &agrave; de nouvelles fonctionnalit&eacute;s qui am&eacute;liorent la productivit&eacute; et la connectivit&eacute;.<br><br>D&eacute;couvrez l\\'avenir des smartphones avec l\\'iPhone 17 Pro, o&ugrave; l\\'innovation rencontre la sophistication.</p>' where `id` = 4822", "duration": 2.5, "duration_str": "2.5s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4795 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `json_value` = '[\\\"product\\/219\\/Cao4K5xVDm023jxUUpSy4GpqoDr1ofoFl0LYIWgM.webp\\\",\\\"product\\/219\\/di36rQui3DFYWDRCwKoADwi0CNDEfCXoe5SzNFuR.webp\\\",\\\"product\\/219\\/jmtxfO3h6C4r1LwAmFwkMmUeENJOOGdEQqJSCaaa.webp\\\"]' where `id` = 4795", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4796 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4821 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4799 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4800 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4801 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4802 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4803 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4805 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 0 where `id` = 4805", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4809 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4810 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4810", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4811 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4811", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4812 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4812", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4813 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4813", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4814 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4814", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 219 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.56, "duration_str": "1.56s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 185 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 186 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 219 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 1.82, "duration_str": "1.82s", "connection": "mlk"}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 219 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.53, "duration_str": "1.53s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 220 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 220 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4733 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4736 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4737 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4737", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4740 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4741 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 220 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 1.71, "duration_str": "1.71s", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 220 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 187 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 221 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 221 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4743 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4746 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4747 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4747", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4750 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4751 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 221 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 221 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 188 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 222 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 222 and `product_attribute_values`.`product_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4753 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4756 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4757 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4757", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4760 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4761 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 222 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 222 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 189 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 223 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 223 and `product_attribute_values`.`product_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4763 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4766 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4767 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4767", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4770 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4771 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 223 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 223 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 190 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 224 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 224 and `product_attribute_values`.`product_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4773 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4776 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4777 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4777", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4780 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4781 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 224 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 224 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 191 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 225 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 225 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4783 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4786 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4787 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4787", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4790 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4791 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 225 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 225 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 192 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `products` where `id` = 219 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (219)", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (219)", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 220 and `product_attribute_values`.`product_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 221 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 222 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 223 and `product_attribute_values`.`product_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 224 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 225 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 219 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 219 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 1.83, "duration_str": "1.83s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 219 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = '4125 IPHONE IP17 PRO', `description` = '<p>Presentiamo l\\'iPhone 17 Pro, una fusione straordinaria di tecnologia all\\'avanguardia e design elegante. Dotato di un\\'impressionante display Super Retina XDR, l\\'iPhone 17 Pro offre colori vivaci e dettagli incredibili, dando vita a ogni immagine e video. Alimentato dal più recente chip A17 Bionic, questo dispositivo offre prestazioni e efficienza fulminee, garantendo un multitasking senza interruzioni e esperienze di gioco coinvolgenti.<br><br>L\\'iPhone 17 Pro vanta un avanzato sistema di tripla fotocamera, che include un obiettivo principale da 48 MP, obiettivi ultra-grandangolari e teleobiettivi, permettendoti di catturare foto e video straordinari in qualsiasi condizione di illuminazione. Con la modalità Notte migliorata e le capacità ProRAW, la tua creatività non conosce limiti. Il dispositivo supporta anche la connettività 5G, offrendo velocità internet fulminee per streaming, giochi e navigazione.<br><br>Progettato pensando alla durata, l\\'iPhone 17 Pro presenta una copertura frontale in Ceramic Shield e un elegante telaio in acciaio inossidabile, rendendolo sia stiloso che resistente. Con iOS 17, goditi un\\'esperienza più personalizzata con nuove funzionalità che migliorano la produttività e la connettività.<br><br>Scopri il futuro degli smartphone con l\\'iPhone 17 Pro, dove innovazione incontra sofisticazione.</p>', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1090", "duration": 4.25, "duration_str": "4.25s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 219 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = '4125 IPHONE IP17 PRO', `description` = '<p>Wir präsentieren das iPhone 17 Pro, eine bemerkenswerte Fusion aus modernster Technologie und elegantem Design. Mit einem atemberaubenden Super Retina XDR Display bietet das iPhone 17 Pro lebendige Farben und unglaubliche Details, die jedes Bild und Video zum Leben erwecken. Angetrieben vom neuesten A17 Bionic Chip bietet dieses Gerät blitzschnelle Leistung und Effizienz und gewährleistet nahtloses Multitasking und immersive Spielerlebnisse.<br><br>Das iPhone 17 Pro verfügt über ein fortschrittliches Dreifach-Kamerasystem, einschließlich eines 48MP-Hauptobjektivs, eines Ultraweitwinkel- und eines Teleobjektivs, mit dem Sie atemberaubende Fotos und Videos bei jeder Lichtbedingung festhalten können. Mit dem verbesserten Nachtmodus und den ProRAW-Funktionen sind Ihrer Kreativität keine Grenzen gesetzt. Das Gerät unterstützt auch die 5G-Konnektivität und bietet blitzschnelle Internetgeschwindigkeiten zum Streamen, Spielen und Surfen.<br><br>Mit Blick auf Langlebigkeit verfügt das iPhone 17 Pro über eine Ceramic Shield-Frontabdeckung und einen eleganten Edelstahlrahmen, der es sowohl stilvoll als auch widerstandsfähig macht. Mit iOS 17 genießen Sie ein personalisierteres Erlebnis mit neuen Funktionen, die Produktivität und Konnektivität verbessern.<br><br>Erleben Sie die Zukunft der Smartphones mit dem iPhone 17 Pro, wo Innovation auf Raffinesse trifft.</p>', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1091", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 219 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `description` = '<p>D&eacute;couvrez l\\'iPhone 17 Pro, une fusion remarquable de technologie de pointe et de design &eacute;l&eacute;gant. Dot&eacute; d\\'un &eacute;cran Super Retina XDR &eacute;pous<PERSON><PERSON><PERSON>, l\\'iPhone 17 Pro offre des couleurs vives et des d&eacute;tails incroyables, rendant chaque image et vid&eacute;o vivante. &Eacute;quip&eacute; de la derni&egrave;re puce A17 Bionic, cet appareil offre des performances et une efficacit&eacute; fulgurantes, garantissant un multit&acirc;che fluide et des exp&eacute;riences de jeu immersives.<br><br>L\\'iPhone 17 Pro dispose d\\'un syst&egrave;me de triple cam&eacute;ra avanc&eacute;, comprenant un objectif principal de 48 MP, ainsi que des objectifs ultra grand angle et t&eacute;l&eacute;objectif, vous permettant de capturer des photos et des vid&eacute;os &eacute;poustouflantes dans toutes les conditions d\\'&eacute;clairage. Avec un mode nuit am&eacute;lior&eacute; et des capacit&eacute;s ProRAW, votre cr&eacute;ativit&eacute; ne conna&icirc;t pas de limites. L\\'appareil prend &eacute;galement en charge la connectivit&eacute; 5G, offrant des vitesses Internet ultrarapides pour le streaming, le jeu et la navigation.<br><br>Con&ccedil;u avec la durabilit&eacute; &agrave; l\\'esprit, l\\'iPhone 17 Pro dispose d\\'un verre Ceramic Shield &agrave; l\\'avant et d\\'un cadre en acier inoxydable &eacute;l&eacute;gant, le rendant &agrave; la fois styl&eacute; et r&eacute;sistant. Avec iOS 17, profitez d\\'une exp&eacute;rience plus personnalis&eacute;e gr&acirc;ce &agrave; de nouvelles fonctionnalit&eacute;s qui am&eacute;liorent la productivit&eacute; et la connectivit&eacute;.<br><br>D&eacute;couvrez l\\'avenir des smartphones avec l\\'iPhone 17 Pro, o&ugrave; l\\'innovation rencontre la sophistication.</p>', `url_key` = '4125-iphone-ip17-pro', `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1092", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 219 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = '4125 IPHONE IP17 PRO', `description` = '<p>Σας παρουσιάζουμε το iPhone 17 Pro, μιαRemarkable fusion of cutting-edge technology and elegant design. Featuring a stunning Super Retina XDR display, the iPhone 17 Pro delivers vibrant colors and incredible detail, making every image and video come to life. Powered by the latest A17 Bionic chip, this device offers lightning-fast performance and efficiency, ensuring seamless multitasking and immersive gaming experiences.<br><br>Το iPhone 17 Pro διαθέτει ένα προηγμένο τριπλό σύστημα κάμερας, συμπεριλαμβανομένου ενός κύριου φακού 48MP, υπερευρυγώνιου και τηλεφακού, επιτρέποντάς σας να αποτυπώνετε εκπληκτικές φωτογραφίες και βίντεο σε οποιαδήποτε συνθήκη φωτισμού. Με βελτιωμένη νυχτερινή λειτουργία και δυνατότητες ProRAW, η δημιουργικότητά σας δεν έχει όρια. Η συσκευή υποστηρίζει επίσης 5G συνδεσιμότητα, παρέχοντας εκπληκτικές ταχύτητες διαδικτύου για streaming, gaming και περιήγηση.<br><br>Σχεδιασμένο με γνώμονα την αντοχή, το iPhone 17 Pro διαθέτει ένα μπροστινό κάλυμμα Ceramic Shield και ένα κομψό πλαίσιο από ανοξείδωτο χάλυβα, κάνοντάς το ταυτόχρονα στιλάτο και ανθεκτικό. Με το iOS 17, απολαύστε μια πιο εξατομικευμένη εμπειρία με νέες δυνατότητες που ενισχύουν την παραγωγικότητα και τη συνδεσιμότητα.<br><br>Ζήστε το μέλλον των smartphones με το iPhone 17 Pro, όπου η καινοτομία συναντά τηSophistication.</p>', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1093", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 220 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 220 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1094", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 220 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1095", "duration": 1.95, "duration_str": "1.95s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 220 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1096", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 220 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 2', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1097", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 220 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1098", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 221 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 221 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1099", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 221 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1100", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 221 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1101", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 221 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 4', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1102", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 221 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1103", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 222 and `product_attribute_values`.`product_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 222 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1104", "duration": 1.96, "duration_str": "1.96s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 222 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1105", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 222 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1106", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 222 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 495', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1107", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 222 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1108", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 223 and `product_attribute_values`.`product_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 223 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1109", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 223 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1110", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 223 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1111", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 223 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 496', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1112", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 223 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1113", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 224 and `product_attribute_values`.`product_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 224 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1114", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 224 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1115", "duration": 1.93, "duration_str": "1.93s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 224 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1116", "duration": 1.97, "duration_str": "1.97s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 224 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 497', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1117", "duration": 1.94, "duration_str": "1.94s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 224 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1118", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 225 and `product_attribute_values`.`product_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 225 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1119", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 225 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1120", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 225 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1121", "duration": 1.96, "duration_str": "1.96s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 225 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `name` = 'Variant 498', `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1122", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 225 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-08-05 04:31:13' where `id` = 1123", "duration": 2.02, "duration_str": "2.02s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.12, "duration_str": "120ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 1.82, "duration_str": "1.82s", "connection": "mlk"}]}]}, "messages": {"count": 1, "messages": [{"message": "[04:31:13] LOG.info: 已分发产品翻译任务 {\n    \"product_id\": 219,\n    \"source_locale\": \"fr\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.446407, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1754364672.96489, "end": **********.695394, "duration": 0.730504035949707, "duration_str": "731ms", "measures": [{"label": "Booting", "start": 1754364672.96489, "relative_start": 0, "end": **********.141414, "relative_end": **********.141414, "duration": 0.*****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.141423, "relative_start": 0.*****************, "end": **********.695396, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "554ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.151015, "relative_start": 0.****************, "end": **********.152378, "relative_end": **********.152378, "duration": 0.0013630390167236328, "duration_str": "1.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.683119, "relative_start": 0.****************, "end": **********.683343, "relative_end": **********.683343, "duration": 0.0002238750457763672, "duration_str": "224μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 289, "nb_statements": 289, "nb_visible_statements": 289, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.20738999999999996, "accumulated_duration_str": "207ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 189 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.167805, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 0.174}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.170649, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.174, "width_percent": 0.082}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.17562, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0.256, "width_percent": 0.926}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.178999, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 1.181, "width_percent": 0.092}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.1799152, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 1.273, "width_percent": 0.058}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.182733, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 1.331, "width_percent": 0.087}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.1846309, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 1.418, "width_percent": 0.878}, {"sql": "select * from `core_config` where `code` = 'catalog.products.attribute.file_attribute_upload_size'", "type": "query", "params": [], "bindings": ["catalog.products.attribute.file_attribute_upload_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.191463, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 2.295, "width_percent": 0.907}, {"sql": "select * from `products` where `products`.`id` = '219' limit 1", "type": "query", "params": [], "bindings": ["219"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.19652, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 3.202, "width_percent": 0.082}, {"sql": "select `id` from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 288}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 313}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.200788, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:288", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=288", "ajax": false, "filename": "Configurable.php", "line": "288"}, "connection": "mlk", "explain": null, "start_percent": 3.284, "width_percent": 0.092}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 546}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2042222, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 3.375, "width_percent": 0.984}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.207653, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 4.359, "width_percent": 0.815}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock')", "type": "query", "params": [], "bindings": [2, "color", "price", "cost", "special_price", "special_price_from", "special_price_to", "length", "width", "height", "weight", "manage_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.210201, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:555", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=555", "ajax": false, "filename": "AbstractType.php", "line": "555"}, "connection": "mlk", "explain": null, "start_percent": 5.174, "width_percent": 0.902}, {"sql": "select count(*) as aggregate from `products` where `sku` = '8034412526218' and `id` <> '219'", "type": "query", "params": [], "bindings": ["8034412526218", "219"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.217207, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 6.076, "width_percent": 0.101}, {"sql": "select exists(select 1 from `category_translations` where `slug` = '4125-iphone-ip17-pro' limit 1) as `exists`", "type": "query", "params": [], "bindings": ["4125-iphone-ip17-pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 77}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}], "start": **********.21858, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductCategoryUniqueSlug.php:103", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FProductCategoryUniqueSlug.php&line=103", "ajax": false, "filename": "ProductCategoryUniqueSlug.php", "line": "103"}, "connection": "mlk", "explain": null, "start_percent": 6.177, "width_percent": 0.082}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "type": "query", "params": [], "bindings": ["catalog.products.search.engine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.220632, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 6.259, "width_percent": 0.159}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 125}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}], "start": **********.222954, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 6.418, "width_percent": 0.092}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = '4125-iphone-ip17-pro'", "type": "query", "params": [], "bindings": [3, "4125-iphone-ip17-pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.22409, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 6.509, "width_percent": 1.095}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 154}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.2286081, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 7.604, "width_percent": 0.077}, {"sql": "select count(`id`) as aggregate from `product_attribute_values` where `text_value` = '4125 IP17 PRO' and `attribute_id` = 27 and `product_id` != '219'", "type": "query", "params": [], "bindings": ["4125 IP17 PRO", 27, "219"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 231}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/ClosureValidationRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ClosureValidationRule.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/ClosureValidationRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ClosureValidationRule.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}], "start": **********.2298908, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:231", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=231", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "231"}, "connection": "mlk", "explain": null, "start_percent": 7.681, "width_percent": 0.121}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.2312388, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 7.802, "width_percent": 0.125}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.232538, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 7.927, "width_percent": 0.106}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.2337298, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 8.033, "width_percent": 0.125}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.234952, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 8.159, "width_percent": 0.111}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.236144, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 8.269, "width_percent": 0.101}, {"sql": "select exists(select * from `products` where `sku` in ('8034412526218-variant-2', '8034412526218-variant-4', '8034412526218-variant-495', '8034412526218-variant-496', '8034412526218-variant-497', '8034412526218-variant-498') and `id` not in (220, 221, 222, 223, 224, 225)) as `exists`", "type": "query", "params": [], "bindings": ["8034412526218-variant-2", "8034412526218-variant-4", "8034412526218-variant-495", "8034412526218-variant-496", "8034412526218-variant-497", "8034412526218-variant-498", 220, 221, 222, 223, 224, 225], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.237321, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 8.371, "width_percent": 0.116}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 42}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.239289, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 8.486, "width_percent": 0.072}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.240335, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 8.559, "width_percent": 0.063}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.2410781, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 8.621, "width_percent": 0.159}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.243847, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 8.781, "width_percent": 0.873}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 219 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 29, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.2463949, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 9.653, "width_percent": 0.111}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 26, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 48}], "start": **********.248967, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 9.764, "width_percent": 0.096}, {"sql": "select * from `url_rewrites` where `entity_type` in ('category', 'product') and `request_path` = '4125-iphone-ip17-pro'", "type": "query", "params": [], "bindings": ["category", "product", "4125-iphone-ip17-pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 53}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.250148, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 9.861, "width_percent": 0.974}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 70}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.252964, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 10.835, "width_percent": 0.063}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.254099, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 10.897, "width_percent": 0.096}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.255153, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 10.994, "width_percent": 0.077}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.255995, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 11.071, "width_percent": 0.164}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 219 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.25719, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 11.235, "width_percent": 0.116}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4793 limit 1", "type": "query", "params": [], "bindings": [4793], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.258372, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 11.351, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4822 limit 1", "type": "query", "params": [], "bindings": [4822], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.26011, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 11.423, "width_percent": 0.092}, {"sql": "update `product_attribute_values` set `text_value` = '<p>D&eacute;couvrez l\\'iPhone 17 Pro, une fusion remarquable de technologie de pointe et de design &eacute;l&eacute;gant. Dot&eacute; d\\'un &eacute;cran Super Retina XDR &eacute;poustouflant, l\\'iPhone 17 Pro offre des couleurs vives et des d&eacute;tails incroyables, rendant chaque image et vid&eacute;o vivante. &Eacute;quip&eacute; de la derni&egrave;re puce A17 Bionic, cet appareil offre des performances et une efficacit&eacute; fulgurantes, garantissant un multit&acirc;che fluide et des exp&eacute;riences de jeu immersives.<br><br>L\\'iPhone 17 Pro dispose d\\'un syst&egrave;me de triple cam&eacute;ra avanc&eacute;, comprenant un objectif principal de 48 MP, ainsi que des objectifs ultra grand angle et t&eacute;l&eacute;objectif, vous permettant de capturer des photos et des vid&eacute;os &eacute;poustouflantes dans toutes les conditions d\\'&eacute;clairage. Avec un mode nuit am&eacute;lior&eacute; et des capacit&eacute;s ProRAW, votre cr&eacute;ativit&eacute; ne conna&icirc;t pas de limites. L\\'appareil prend &eacute;galement en charge la connectivit&eacute; 5G, offrant des vitesses Internet ultrarapides pour le streaming, le jeu et la navigation.<br><br>Con&ccedil;u avec la durabilit&eacute; &agrave; l\\'esprit, l\\'iPhone 17 Pro dispose d\\'un verre Ceramic Shield &agrave; l\\'avant et d\\'un cadre en acier inoxydable &eacute;l&eacute;gant, le rendant &agrave; la fois styl&eacute; et r&eacute;sistant. Avec iOS 17, profitez d\\'une exp&eacute;rience plus personnalis&eacute;e gr&acirc;ce &agrave; de nouvelles fonctionnalit&eacute;s qui am&eacute;liorent la productivit&eacute; et la connectivit&eacute;.<br><br>D&eacute;couvrez l\\'avenir des smartphones avec l\\'iPhone 17 Pro, o&ugrave; l\\'innovation rencontre la sophistication.</p>' where `id` = 4822", "type": "query", "params": [], "bindings": ["<p>D&eacute;couvrez l'iPhone 17 Pro, une fusion remarquable de technologie de pointe et de design &eacute;l&eacute;gant. Dot&eacute; d'un &eacute;cran Super Retina XDR &eacute;pous<PERSON><PERSON><PERSON>, l'iPhone 17 Pro offre des couleurs vives et des d&eacute;tails incroyables, rendant chaque image et vid&eacute;o vivante. &Eacute;quip&eacute; de la derni&egrave;re puce A17 Bionic, cet appareil offre des performances et une efficacit&eacute; fulgurantes, garantissant un multit&acirc;che fluide et des exp&eacute;riences de jeu immersives.<br><br>L'iPhone 17 Pro dispose d'un syst&egrave;me de triple cam&eacute;ra avanc&eacute;, comprenant un objectif principal de 48 MP, ainsi que des objectifs ultra grand angle et t&eacute;l&eacute;objectif, vous permettant de capturer des photos et des vid&eacute;os &eacute;poustouflantes dans toutes les conditions d'&eacute;clairage. Avec un mode nuit am&eacute;lior&eacute; et des capacit&eacute;s ProRAW, votre cr&eacute;ativit&eacute; ne conna&icirc;t pas de limites. L'appareil prend &eacute;galement en charge la connectivit&eacute; 5G, offrant des vitesses Internet ultrarapides pour le streaming, le jeu et la navigation.<br><br>Con&ccedil;u avec la durabilit&eacute; &agrave; l'esprit, l'iPhone 17 Pro dispose d'un verre Ceramic Shield &agrave; l'avant et d'un cadre en acier inoxydable &eacute;l&eacute;gant, le rendant &agrave; la fois styl&eacute; et r&eacute;sistant. Avec iOS 17, profitez d'une exp&eacute;rience plus personnalis&eacute;e gr&acirc;ce &agrave; de nouvelles fonctionnalit&eacute;s qui am&eacute;liorent la productivit&eacute; et la connectivit&eacute;.<br><br>D&eacute;couvrez l'avenir des smartphones avec l'iPhone 17 Pro, o&ugrave; l'innovation rencontre la sophistication.</p>", 4822], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2610738, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 11.515, "width_percent": 1.205}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4795 limit 1", "type": "query", "params": [], "bindings": [4795], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.26596, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 12.72, "width_percent": 0.087}, {"sql": "update `product_attribute_values` set `json_value` = '[\\\"product\\\\/219\\\\/Cao4K5xVDm023jxUUpSy4GpqoDr1ofoFl0LYIWgM.webp\\\",\\\"product\\\\/219\\\\/di36rQui3DFYWDRCwKoADwi0CNDEfCXoe5SzNFuR.webp\\\",\\\"product\\\\/219\\\\/jmtxfO3h6C4r1LwAmFwkMmUeENJOOGdEQqJSCaaa.webp\\\"]' where `id` = 4795", "type": "query", "params": [], "bindings": ["[\"product\\/219\\/Cao4K5xVDm023jxUUpSy4GpqoDr1ofoFl0LYIWgM.webp\",\"product\\/219\\/di36rQui3DFYWDRCwKoADwi0CNDEfCXoe5SzNFuR.webp\",\"product\\/219\\/jmtxfO3h6C4r1LwAmFwkMmUeENJOOGdEQqJSCaaa.webp\"]", 4795], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.266879, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 12.807, "width_percent": 0.993}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4796 limit 1", "type": "query", "params": [], "bindings": [4796], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2702289, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 13.8, "width_percent": 0.101}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4821 limit 1", "type": "query", "params": [], "bindings": [4821], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.271545, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 13.901, "width_percent": 0.068}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4799 limit 1", "type": "query", "params": [], "bindings": [4799], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.272906, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 13.969, "width_percent": 0.068}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4800 limit 1", "type": "query", "params": [], "bindings": [4800], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.274065, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.036, "width_percent": 0.101}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4801 limit 1", "type": "query", "params": [], "bindings": [4801], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.275331, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.138, "width_percent": 0.077}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4802 limit 1", "type": "query", "params": [], "bindings": [4802], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.276504, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.215, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4803 limit 1", "type": "query", "params": [], "bindings": [4803], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.277628, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.287, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4805 limit 1", "type": "query", "params": [], "bindings": [4805], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.278984, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.359, "width_percent": 0.068}, {"sql": "update `product_attribute_values` set `boolean_value` = 0 where `id` = 4805", "type": "query", "params": [], "bindings": [0, 4805], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2797992, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 14.427, "width_percent": 0.979}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4809 limit 1", "type": "query", "params": [], "bindings": [4809], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.283791, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 15.406, "width_percent": 0.077}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4810 limit 1", "type": "query", "params": [], "bindings": [4810], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.285081, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 15.483, "width_percent": 0.077}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4810", "type": "query", "params": [], "bindings": [1, 4810], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.285951, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 15.56, "width_percent": 0.935}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4811 limit 1", "type": "query", "params": [], "bindings": [4811], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2890918, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 16.495, "width_percent": 0.111}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4811", "type": "query", "params": [], "bindings": [1, 4811], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2901442, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 16.606, "width_percent": 0.984}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4812 limit 1", "type": "query", "params": [], "bindings": [4812], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.293422, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 17.59, "width_percent": 0.077}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4812", "type": "query", "params": [], "bindings": [1, 4812], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.294276, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 17.667, "width_percent": 0.964}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4813 limit 1", "type": "query", "params": [], "bindings": [4813], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.297458, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 18.632, "width_percent": 0.082}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4813", "type": "query", "params": [], "bindings": [1, 4813], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.2982998, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 18.714, "width_percent": 0.964}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4814 limit 1", "type": "query", "params": [], "bindings": [4814], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.301452, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 19.678, "width_percent": 0.077}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4814", "type": "query", "params": [], "bindings": [1, 4814], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.30231, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 19.755, "width_percent": 0.95}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (3, null, null, null, null, null, null, null, 'fr', 219, '4125-iphone-ip17-pro', 'fr|219|3'), (39, null, null, null, null, null, null, null, 'fr', 219, '', 'fr|219|39'), (16, null, null, null, null, null, null, null, 'fr', 219, '', 'fr|219|16'), (17, null, null, null, null, null, null, null, 'fr', 219, '', 'fr|219|17'), (18, null, null, null, null, null, null, null, 'fr', 219, '', 'fr|219|18')", "type": "query", "params": [], "bindings": [3, null, null, null, null, null, null, null, "fr", 219, "4125-iphone-ip17-pro", "fr|219|3", 39, null, null, null, null, null, null, null, "fr", 219, "", "fr|219|39", 16, null, null, null, null, null, null, null, "fr", 219, "", "fr|219|16", 17, null, null, null, null, null, null, null, "fr", 219, "", "fr|219|17", 18, null, null, null, null, null, null, null, "fr", 219, "", "fr|219|18"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.3053348, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 20.705, "width_percent": 1.302}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.309038, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:173", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=173", "ajax": false, "filename": "AbstractType.php", "line": "173"}, "connection": "mlk", "explain": null, "start_percent": 22.007, "width_percent": 0.921}, {"sql": "select * from `product_categories` where `product_categories`.`product_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.312562, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:179", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=179", "ajax": false, "filename": "AbstractType.php", "line": "179"}, "connection": "mlk", "explain": null, "start_percent": 22.928, "width_percent": 0.829}, {"sql": "select * from `product_up_sells` where `product_up_sells`.`parent_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.315142, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:181", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=181", "ajax": false, "filename": "AbstractType.php", "line": "181"}, "connection": "mlk", "explain": null, "start_percent": 23.757, "width_percent": 0.752}, {"sql": "select * from `product_cross_sells` where `product_cross_sells`.`parent_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.317536, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:183", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=183", "ajax": false, "filename": "AbstractType.php", "line": "183"}, "connection": "mlk", "explain": null, "start_percent": 24.509, "width_percent": 0.757}, {"sql": "select * from `product_relations` where `product_relations`.`parent_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.319973, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:185", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=185", "ajax": false, "filename": "AbstractType.php", "line": "185"}, "connection": "mlk", "explain": null, "start_percent": 25.266, "width_percent": 0.767}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 219 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.3223531, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 26.033, "width_percent": 0.752}, {"sql": "select * from `product_images` where `product_images`.`id` = 185 limit 1", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.324861, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 26.785, "width_percent": 0.077}, {"sql": "select * from `product_images` where `product_images`.`id` = 186 limit 1", "type": "query", "params": [], "bindings": [186], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.325822, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 26.862, "width_percent": 0.058}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 219 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 191}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.326725, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 26.92, "width_percent": 0.878}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 219 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 193}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.329309, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ProductCustomerGroupPriceRepository.php:24", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductCustomerGroupPriceRepository.php&line=24", "ajax": false, "filename": "ProductCustomerGroupPriceRepository.php", "line": "24"}, "connection": "mlk", "explain": null, "start_percent": 27.798, "width_percent": 0.738}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "type": "query", "params": [], "bindings": ["sku", "name", "url_key", "short_description", "description", "price", "weight", "status", "tax_category_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3317142, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 28.536, "width_percent": 0.135}, {"sql": "select * from `products` where `products`.`parent_id` = 219 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.332838, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 28.671, "width_percent": 0.077}, {"sql": "select * from `products` where `products`.`id` = 220 limit 1", "type": "query", "params": [], "bindings": [220], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 234}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.3337162, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 28.748, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 220 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [220], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.334604, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 28.81, "width_percent": 0.096}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4733 limit 1", "type": "query", "params": [], "bindings": [4733], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.335676, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 28.907, "width_percent": 0.082}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4736 limit 1", "type": "query", "params": [], "bindings": [4736], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.337036, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 28.989, "width_percent": 0.082}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4737 limit 1", "type": "query", "params": [], "bindings": [4737], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.338116, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 29.071, "width_percent": 0.068}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4737", "type": "query", "params": [], "bindings": [1, 4737], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.3389199, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 29.138, "width_percent": 0.964}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4740 limit 1", "type": "query", "params": [], "bindings": [4740], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.342272, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 30.103, "width_percent": 0.092}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4741 limit 1", "type": "query", "params": [], "bindings": [4741], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.34342, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 30.194, "width_percent": 0.072}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (2, null, null, null, null, null, null, null, 'fr', 220, 'Variant 2', 'fr|220|2')", "type": "query", "params": [], "bindings": [2, null, null, null, null, null, null, null, "fr", 220, "Variant 2", "fr|220|2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.344333, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 30.267, "width_percent": 0.96}, {"sql": "select * from `product_inventories` where (`product_id` = 220 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "type": "query", "params": [], "bindings": [220, 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductInventoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductInventoryRepository.php", "line": 28}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 240}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.3474832, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 31.226, "width_percent": 0.825}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 220 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [220], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.350133, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 32.051, "width_percent": 0.072}, {"sql": "select * from `product_images` where `product_images`.`id` = 187 limit 1", "type": "query", "params": [], "bindings": [187], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.351016, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 32.123, "width_percent": 0.096}, {"sql": "select * from `products` where `products`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.352181, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 32.219, "width_percent": 0.068}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 219", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.35306, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 32.287, "width_percent": 0.897}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 220", "type": "query", "params": [], "bindings": [220], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.355662, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:244", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=244", "ajax": false, "filename": "Configurable.php", "line": "244"}, "connection": "mlk", "explain": null, "start_percent": 33.184, "width_percent": 0.068}, {"sql": "select * from `products` where `products`.`id` = 221 limit 1", "type": "query", "params": [], "bindings": [221], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 234}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.356532, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 33.251, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 221 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [221], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.357432, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 33.314, "width_percent": 0.087}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4743 limit 1", "type": "query", "params": [], "bindings": [4743], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.358422, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 33.401, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4746 limit 1", "type": "query", "params": [], "bindings": [4746], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.359648, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 33.464, "width_percent": 0.077}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4747 limit 1", "type": "query", "params": [], "bindings": [4747], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.360726, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 33.541, "width_percent": 0.072}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4747", "type": "query", "params": [], "bindings": [1, 4747], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.361558, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 33.613, "width_percent": 0.96}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4750 limit 1", "type": "query", "params": [], "bindings": [4750], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.364917, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 34.573, "width_percent": 0.096}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4751 limit 1", "type": "query", "params": [], "bindings": [4751], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.366197, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 34.669, "width_percent": 0.082}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (2, null, null, null, null, null, null, null, 'fr', 221, 'Variant 4', 'fr|221|2')", "type": "query", "params": [], "bindings": [2, null, null, null, null, null, null, null, "fr", 221, "Variant 4", "fr|221|2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.3671439, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:190", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=190", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "190"}, "connection": "mlk", "explain": null, "start_percent": 34.751, "width_percent": 0.945}, {"sql": "select * from `product_inventories` where (`product_id` = ? and `inventory_source_id` = ? and `vendor_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37002, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 35.696, "width_percent": 0.106}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = ? and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.370537, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 35.802, "width_percent": 0.096}, {"sql": "select * from `product_images` where `product_images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37086, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 35.899, "width_percent": 0.063}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3712602, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 35.961, "width_percent": 0.063}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.371562, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.024, "width_percent": 0.082}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.371882, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.106, "width_percent": 0.058}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372111, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.164, "width_percent": 0.058}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372453, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.222, "width_percent": 0.082}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372906, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.304, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.373601, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.366, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.374118, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.429, "width_percent": 0.068}, {"sql": "update `product_attribute_values` set `boolean_value` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37441, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 36.496, "width_percent": 0.969}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.376934, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 37.466, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.377458, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 37.538, "width_percent": 0.068}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.377824, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 37.605, "width_percent": 0.945}, {"sql": "select * from `product_inventories` where (`product_id` = ? and `inventory_source_id` = ? and `vendor_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.379924, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.551, "width_percent": 0.082}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = ? and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380392, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.633, "width_percent": 0.087}, {"sql": "select * from `product_images` where `product_images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3807201, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.719, "width_percent": 0.072}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381122, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.792, "width_percent": 0.058}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381403, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.85, "width_percent": 0.082}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381722, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 38.931, "width_percent": 0.149}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38218, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.081, "width_percent": 0.092}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3826518, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.173, "width_percent": 0.121}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.383215, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.293, "width_percent": 0.077}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.383975, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.37, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384521, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.443, "width_percent": 0.063}, {"sql": "update `product_attribute_values` set `boolean_value` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384813, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 39.505, "width_percent": 0.935}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.387343, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 40.441, "width_percent": 0.106}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388019, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 40.547, "width_percent": 0.072}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388416, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 40.619, "width_percent": 0.984}, {"sql": "select * from `product_inventories` where (`product_id` = ? and `inventory_source_id` = ? and `vendor_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390615, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.603, "width_percent": 0.087}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = ? and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391052, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.69, "width_percent": 0.077}, {"sql": "select * from `product_images` where `product_images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391329, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.767, "width_percent": 0.063}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391727, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.829, "width_percent": 0.068}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392034, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.897, "width_percent": 0.082}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392349, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 41.979, "width_percent": 0.058}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392574, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.037, "width_percent": 0.058}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392912, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.095, "width_percent": 0.082}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393358, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.177, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394052, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.239, "width_percent": 0.058}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3945642, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.297, "width_percent": 0.063}, {"sql": "update `product_attribute_values` set `boolean_value` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394848, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 42.36, "width_percent": 1.003}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397472, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 43.363, "width_percent": 0.101}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.398178, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 43.464, "width_percent": 0.072}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.398583, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 43.536, "width_percent": 0.96}, {"sql": "select * from `product_inventories` where (`product_id` = ? and `inventory_source_id` = ? and `vendor_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400726, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.496, "width_percent": 0.096}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = ? and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401228, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.592, "width_percent": 0.092}, {"sql": "select * from `product_images` where `product_images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401566, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.684, "width_percent": 0.072}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402057, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.756, "width_percent": 0.077}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402428, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.833, "width_percent": 0.111}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402817, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 44.944, "width_percent": 0.058}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403042, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.002, "width_percent": 0.058}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403385, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.06, "width_percent": 0.111}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403924, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.171, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404649, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.243, "width_percent": 0.063}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405165, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.306, "width_percent": 0.068}, {"sql": "update `product_attribute_values` set `boolean_value` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4054618, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 45.373, "width_percent": 0.964}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407957, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 46.338, "width_percent": 0.077}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408494, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 46.415, "width_percent": 0.068}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `json_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408848, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 46.482, "width_percent": 0.921}, {"sql": "select * from `product_inventories` where (`product_id` = ? and `inventory_source_id` = ? and `vendor_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410876, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.403, "width_percent": 0.082}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = ? and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411332, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.485, "width_percent": 0.092}, {"sql": "select * from `product_images` where `product_images`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411661, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.577, "width_percent": 0.077}, {"sql": "select * from `products` where `products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4120882, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.654, "width_percent": 0.058}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412364, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.712, "width_percent": 0.092}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412698, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.804, "width_percent": 0.092}, {"sql": "select * from `products` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413013, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.895, "width_percent": 0.092}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413418, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 47.987, "width_percent": 0.072}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (219)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4137378, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 48.059, "width_percent": 0.125}, {"sql": "select * from `products` where `products`.`parent_id` in (219)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414361, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 48.185, "width_percent": 0.072}, {"sql": "select * from `core_config` where `code` = ? and `channel_code` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417427, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 48.257, "width_percent": 0.911}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4404001, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 49.168, "width_percent": 2.03}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4477391, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 51.198, "width_percent": 0.979}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451205, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 52.177, "width_percent": 0.13}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451677, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 52.307, "width_percent": 0.068}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453552, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 52.375, "width_percent": 1.538}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4570599, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 53.913, "width_percent": 0.101}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.457776, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.014, "width_percent": 0.063}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459567, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.077, "width_percent": 0.092}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460001, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.168, "width_percent": 0.087}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460575, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.255, "width_percent": 0.058}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4622009, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.313, "width_percent": 0.096}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.46264, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.41, "width_percent": 0.092}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.463236, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.501, "width_percent": 0.063}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.464877, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.564, "width_percent": 0.111}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465357, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.675, "width_percent": 0.082}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465933, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.757, "width_percent": 0.058}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467537, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.815, "width_percent": 0.096}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467977, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 54.911, "width_percent": 0.092}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.468578, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 55.003, "width_percent": 0.058}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4701378, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 55.061, "width_percent": 0.111}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4706059, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 55.171, "width_percent": 0.096}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.472462, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 55.268, "width_percent": 0.096}, {"sql": "select * from `currencies` where `currencies`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4767852, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 55.364, "width_percent": 0.81}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.481215, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.174, "width_percent": 0.275}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.481973, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.449, "width_percent": 1.692}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.485889, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 58.142, "width_percent": 0.087}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4862409, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 58.228, "width_percent": 0.111}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.487859, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 58.339, "width_percent": 0.092}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4883049, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 58.431, "width_percent": 0.882}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.490274, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 59.313, "width_percent": 0.26}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.494217, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 59.574, "width_percent": 0.106}, {"sql": "update `product_flat` set `name` = ?, `description` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.497755, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 59.68, "width_percent": 2.049}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502303, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 61.729, "width_percent": 0.111}, {"sql": "update `product_flat` set `name` = ?, `description` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.505943, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 61.84, "width_percent": 0.998}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5083039, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.838, "width_percent": 0.111}, {"sql": "update `product_flat` set `description` = ?, `url_key` = ?, `meta_title` = ?, `meta_keywords` = ?, `meta_description` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.512206, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.949, "width_percent": 1.003}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514586, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 63.952, "width_percent": 0.111}, {"sql": "update `product_flat` set `name` = ?, `description` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.518121, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 64.063, "width_percent": 1.003}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.521334, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.066, "width_percent": 0.116}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.521851, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.182, "width_percent": 0.092}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.522232, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.273, "width_percent": 0.096}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.523644, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.37, "width_percent": 0.101}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.524097, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.471, "width_percent": 0.082}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5263422, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.553, "width_percent": 0.974}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528638, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.527, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.53096, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.638, "width_percent": 0.94}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.53318, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 67.578, "width_percent": 0.106}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5355759, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 67.684, "width_percent": 0.969}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537838, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 68.653, "width_percent": 0.101}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5405252, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 68.755, "width_percent": 0.974}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.542765, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 69.729, "width_percent": 0.106}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545222, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 69.835, "width_percent": 0.95}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5475159, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.785, "width_percent": 0.116}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.547964, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.9, "width_percent": 0.087}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549311, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 70.987, "width_percent": 0.101}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.54975, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.088, "width_percent": 0.092}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5519838, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 71.18, "width_percent": 0.96}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554204, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.139, "width_percent": 0.096}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.556468, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.236, "width_percent": 0.969}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5587668, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.205, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5612128, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.316, "width_percent": 0.969}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563515, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.285, "width_percent": 0.106}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565809, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 74.391, "width_percent": 1.008}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5681798, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.399, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5705578, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.51, "width_percent": 0.95}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.572821, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.46, "width_percent": 0.111}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5732858, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.571, "width_percent": 0.111}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.574794, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.682, "width_percent": 0.101}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.575249, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.783, "width_percent": 0.082}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.577501, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.865, "width_percent": 0.945}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.579723, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.81, "width_percent": 0.106}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.582042, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.916, "width_percent": 0.95}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5842729, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 78.866, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.586643, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 78.977, "width_percent": 0.984}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.588914, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 79.96, "width_percent": 0.111}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.591348, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 80.071, "width_percent": 0.96}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5936048, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 81.031, "width_percent": 0.116}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5958412, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 81.147, "width_percent": 0.979}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.598119, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.125, "width_percent": 0.087}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.598476, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.212, "width_percent": 0.087}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.599783, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.299, "width_percent": 0.101}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.600229, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.4, "width_percent": 0.082}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.602455, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.482, "width_percent": 0.969}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604691, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 83.451, "width_percent": 0.101}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6071892, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 83.553, "width_percent": 0.988}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.609508, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.541, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6117918, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.652, "width_percent": 0.96}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.614055, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.612, "width_percent": 0.106}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616639, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.718, "width_percent": 0.964}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618906, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 86.682, "width_percent": 0.106}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621305, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 86.788, "width_percent": 0.935}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6235578, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.724, "width_percent": 0.116}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.623993, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.839, "width_percent": 0.096}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625316, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.936, "width_percent": 0.092}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625737, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.027, "width_percent": 0.082}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6279502, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.109, "width_percent": 0.979}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630255, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.088, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632794, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.199, "width_percent": 0.931}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.634966, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.13, "width_percent": 0.111}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637363, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.241, "width_percent": 0.95}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639607, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.191, "width_percent": 0.106}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641844, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.297, "width_percent": 0.935}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644006, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.232, "width_percent": 0.092}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.647336, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.324, "width_percent": 0.974}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649694, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.298, "width_percent": 0.116}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650121, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.413, "width_percent": 0.101}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651557, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.515, "width_percent": 0.092}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6519809, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.606, "width_percent": 0.087}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6543212, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.693, "width_percent": 0.988}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6566548, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.682, "width_percent": 0.116}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6589632, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.797, "width_percent": 0.974}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661247, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.771, "width_percent": 0.106}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663625, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.877, "width_percent": 0.945}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665845, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.822, "width_percent": 0.111}, {"sql": "update `product_flat` set `name` = ?, `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668636, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.933, "width_percent": 0.998}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6709878, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.931, "width_percent": 0.116}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6736772, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.047, "width_percent": 0.974}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679033, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 99.021, "width_percent": 0.979}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 388, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 128, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 81, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 47, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductFlat": {"value": 35, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductFlat.php&line=1", "ajax": false, "filename": "ProductFlat.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventory": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventory.php&line=1", "ajax": false, "filename": "ProductInventory.php", "line": "?"}}, "Webkul\\Core\\Models\\CoreConfig": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 731, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/products/edit/219?channel=default&locale=fr", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update", "uri": "PUT admin/catalog/products/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:149-160</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "741ms", "peak_memory": "46MB", "response": "Redirect to http://mlk.test/admin/catalog/products", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1719249203 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719249203\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344060154 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B11xWJFCjCSYtrnB3VbEm135r5tEhSr3xCforiNm</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">8034412526218</span>\"\n  \"<span class=sf-dump-key>group_code</span>\" => \"\"\n  \"<span class=sf-dump-key>product_number</span>\" => \"<span class=sf-dump-str title=\"13 characters\">4125 IP17 PRO</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">4125 IPHONE IP17 PRO</span>\"\n  \"<span class=sf-dump-key>url_key</span>\" => \"<span class=sf-dump-str title=\"20 characters\">4125-iphone-ip17-pro</span>\"\n  \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"3 characters\">371</span>\"\n  \"<span class=sf-dump-key>device</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">383</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>profile</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>DROP_rating</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Experience the future of smartphones with the iPhone 17 Pro</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"1740 characters\">&lt;p&gt;D&amp;eacute;couvrez l&#039;iPhone 17 Pro, une fusion remarquable de technologie de pointe et de design &amp;eacute;l&amp;eacute;gant. Dot&amp;eacute; d&#039;un &amp;eacute;cran Super Retina XDR &amp;eacute;poustouflant, l&#039;iPhone 17 Pro offre des couleurs vives et des d&amp;eacute;tails incroyables, rendant chaque image et vid&amp;eacute;o vivante. &amp;Eacute;quip&amp;eacute; de la derni&amp;egrave;re puce A17 Bionic, cet appareil offre des performances et une efficacit&amp;eacute; fulgurantes, garantissant un multit&amp;acirc;che fluide et des exp&amp;eacute;riences de jeu immersives.&lt;br&gt;&lt;br&gt;L&#039;iPhone 17 Pro dispose d&#039;un syst&amp;egrave;me de triple cam&amp;eacute;ra avanc&amp;eacute;, comprenant un objectif principal de 48 MP, ainsi que des objectifs ultra grand angle et t&amp;eacute;l&amp;eacute;objectif, vous permettant de capturer des photos et des vid&amp;eacute;os &amp;eacute;poustouflantes dans toutes les conditions d&#039;&amp;eacute;clairage. Avec un mode nuit am&amp;eacute;lior&amp;eacute; et des capacit&amp;eacute;s ProRAW, votre cr&amp;eacute;ativit&amp;eacute; ne conna&amp;icirc;t pas de limites. L&#039;appareil prend &amp;eacute;galement en charge la connectivit&amp;eacute; 5G, offrant des vitesses Internet ultrarapides pour le streaming, le jeu et la navigation.&lt;br&gt;&lt;br&gt;Con&amp;ccedil;u avec la durabilit&amp;eacute; &amp;agrave; l&#039;esprit, l&#039;iPhone 17 Pro dispose d&#039;un verre Ceramic Shield &amp;agrave; l&#039;avant et d&#039;un cadre en acier inoxydable &amp;eacute;l&amp;eacute;gant, le rendant &amp;agrave; la fois styl&amp;eacute; et r&amp;eacute;sistant. Avec iOS 17, profitez d&#039;une exp&amp;eacute;rience plus personnalis&amp;eacute;e gr&amp;acirc;ce &amp;agrave; de nouvelles fonctionnalit&amp;eacute;s qui am&amp;eacute;liorent la productivit&amp;eacute; et la connectivit&amp;eacute;.&lt;br&gt;&lt;br&gt;D&amp;eacute;couvrez l&#039;avenir des smartphones avec l&#039;iPhone 17 Pro, o&amp;ugrave; l&#039;innovation rencontre la sophistication.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>meta_title</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_keywords</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_description</span>\" => \"\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>185</span> => \"\"\n      <span class=sf-dump-key>186</span> => \"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>scene</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product/219/Cao4K5xVDm023jxUUpSy4GpqoDr1ofoFl0LYIWgM.webp</span>\" => \"\"\n      \"<span class=sf-dump-key>product/219/di36rQui3DFYWDRCwKoADwi0CNDEfCXoe5SzNFuR.webp</span>\" => \"\"\n      \"<span class=sf-dump-key>product/219/jmtxfO3h6C4r1LwAmFwkMmUeENJOOGdEQqJSCaaa.webp</span>\" => \"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>variants</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>220</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"23 characters\">8034412526218-variant-2</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Variant 2</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"6 characters\">528000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>187</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>221</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"23 characters\">8034412526218-variant-4</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Variant 4</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"7 characters\">1056000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>188</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>222</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"25 characters\">8034412526218-variant-495</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant 495</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"3 characters\">495</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"7 characters\">1320000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>189</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>223</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"25 characters\">8034412526218-variant-496</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant 496</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"3 characters\">496</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"7 characters\">1056000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>190</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>224</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"25 characters\">8034412526218-variant-497</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant 497</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"3 characters\">497</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"6 characters\">528000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>191</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>225</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"25 characters\">8034412526218-variant-498</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant 498</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1.0000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"3 characters\">498</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"6 characters\">792000</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>192</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>tax_category_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>featured</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>visible_individually</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>guest_checkout</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>channels</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344060154\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-487887048 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImFNQjk2NlZSTkhGZE5vSWNySzFNemc9PSIsInZhbHVlIjoiRnpsa3RKTWVTVlhsUXBYRFBzUFV0V0hPOS9Ba1UxSkUzalVBWnlsZzQrdHV6OEFLZkdsTU05cFpkRFBVclJMOFpCQTNMNlY1RkVxZVAzdDlPUUgzTDl5cHAzWnB3MlVIRmNhdElPaWIvWU44YkxsS2p5TDIvQS9rNFBYNWdnTW0iLCJtYWMiOiIyNTliNmE5MGM2MzI2YWQ0NmZiZmJhMTljYzkxMzE0NzBhOWRmZDhjZjFjYzA3ZmI1Y2I5OWZiNzNkYzE4ZWQ2IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkxtSFpkelUzTUNpazRURTBkTEdkTkE9PSIsInZhbHVlIjoiQ0RqUXpoTHphUmZVMFkrVEZVei91eGk1YVNmNi95QnFObENqdUl2dWRiSjArVHFWWnh5N2hoK2tJdjNTZjVjd2VsRWZlaXNnZ2FLNzZZc0tBMHFzQlMvUjl1MzVmYTdCU0RZUUc4K3JLNFNyN1Z3c2ZadjdrWWg3YXJNbzZTWmsiLCJtYWMiOiI1MDM0MjY4MDUxYzkzNGZkYWU0MmY4MzQ2YjdhMzExMDk2NjExMzQ3NzU2ZmUwZjY2Mjg1NDk2YTkzZjdlYmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/admin/catalog/products/edit/219?channel=default&amp;locale=fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryiCmcBUcnpXRlDCuO</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">12550</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487887048\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-611379721 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B11xWJFCjCSYtrnB3VbEm135r5tEhSr3xCforiNm</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U5hTQKzHHDQQHziQSvVlqKNanG9uIfIfK7rmRRCC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611379721\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-482516324 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:31:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482516324\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1443521174 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B11xWJFCjCSYtrnB3VbEm135r5tEhSr3xCforiNm</span>\"\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/admin/catalog/products/edit/219?channel=default&amp;locale=fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#20135;&#21697;&#26356;&#26032;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443521174\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/products/edit/219?channel=default&locale=fr", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update"}, "badge": "302 Found"}}