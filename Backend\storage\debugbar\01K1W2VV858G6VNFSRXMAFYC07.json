{"__meta": {"id": "01K1W2VV858G6VNFSRXMAFYC07", "datetime": "2025-08-05 04:02:12", "utime": **********.485714, "method": "POST", "uri": "/api/mlk/category/by-categories", "ip": "127.0.0.1"}, "modules": {"count": 6, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (114)", "Webkul\\Attribute\\Models\\AttributeOption (46)", "Webkul\\Attribute\\Models\\AttributeOptionTranslation (240)", "Webkul\\Attribute\\Models\\AttributeTranslation (22)", "Webkul\\Attribute\\Models\\AttributeFamily (3)"], "views": [], "queries": [{"sql": "select `attributes`.*, `category_filterable_attributes`.`category_id` as `pivot_category_id`, `category_filterable_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `category_filterable_attributes` on `attributes`.`id` = `category_filterable_attributes`.`attribute_id` where `category_filterable_attributes`.`category_id` = 10", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (11, 23, 24, 25) order by `sort_order` asc", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 495, 496, 497, 498)", "duration": 0.72, "duration_str": "720ms", "connection": "mlk"}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (11, 23, 24, 25)", "duration": 2.06, "duration_str": "2.06s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 23 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 4 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 495 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 496 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 497 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 498 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 1 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 2 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 3 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 5 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 24 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 6 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 7 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 8 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 9 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 372 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 373 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 374 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 375 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 376 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 377 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 378 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 379 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 380 and `attribute_option_translations`.`attribute_option_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('category_ids', 'page', 'limit', 'sort', 'order', 'category_id', 'status', 'visible_individually', 'url_key')", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'price'", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 1.96, "duration_str": "1.96s", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 1.8, "duration_str": "1.8s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 3.44, "duration_str": "3.44s", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}, {"name": "Webkul\\CatalogRule", "models": [], "views": [], "queries": [{"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1 and `rule_date` = '2025-08-05'", "duration": 1.83, "duration_str": "1.83s", "connection": "mlk"}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1 and `rule_date` = '2025-08-05'", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (23)", "Webkul\\Category\\Models\\CategoryTranslation (65)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `categories` where `id` = 10", "duration": 2.01, "duration_str": "2.01s", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 1.98, "duration_str": "1.98s", "connection": "mlk"}, {"sql": "select `_lft`, `_rgt` from `categories` where `id` = 10 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 14 and 27)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10, 17, 18, 19, 20, 21, 22)", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `categories` where `id` in (10)", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `categories` where `id` in (10)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 1.74, "duration_str": "1.74s", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 1.77, "duration_str": "1.77s", "connection": "mlk"}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (6)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.86, "duration_str": "1.86s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "duration": 1.73, "duration_str": "1.73s", "connection": "mlk"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 2.19, "duration_str": "2.19s", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (6)", "Webkul\\Product\\Models\\ProductImage (18)", "Webkul\\Product\\Models\\ProductAttributeValue (666)", "Webkul\\Product\\Models\\ProductPriceIndex (30)", "Webkul\\Product\\Models\\ProductInventoryIndex (6)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_categories` on `product_categories`.`product_id` = `products`.`id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_categories`.`category_id` in ('10', '17', '18', '19', '20', '21', '22') and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `product_price_indices`.`min_price` desc limit 6 offset 0", "duration": 1.32, "duration_str": "1.32s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 1.93, "duration_str": "1.93s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.66, "duration_str": "660ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 1.89, "duration_str": "1.89s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.71, "duration_str": "710ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `customer_group_id` = 1 order by `qty` asc", "duration": 1.84, "duration_str": "1.84s", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.69, "duration_str": "690ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `customer_group_id` = 1 order by `qty` asc", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754362931.929465, "end": **********.493799, "duration": 0.5643339157104492, "duration_str": "564ms", "measures": [{"label": "Booting", "start": 1754362931.929465, "relative_start": 0, "end": **********.10782, "relative_end": **********.10782, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.10783, "relative_start": 0.*****************, "end": **********.493801, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.120497, "relative_start": 0.*****************, "end": **********.12348, "relative_end": **********.12348, "duration": 0.*****************, "duration_str": "2.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.48396, "relative_start": 0.****************, "end": **********.484254, "relative_end": **********.484254, "duration": 0.00029397010803222656, "duration_str": "294μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.484272, "relative_start": 0.****************, "end": **********.484285, "relative_end": **********.484285, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "fr"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 163, "nb_statements": 163, "nb_visible_statements": 163, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0788600000000001, "accumulated_duration_str": "78.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 63 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}], "start": **********.137808, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.359}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.1437979, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 2.359, "width_percent": 0.304}, {"sql": "select count(*) as aggregate from `categories` where `id` = 10", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.147655, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 2.663, "width_percent": 2.549}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 161}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1518009, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 5.212, "width_percent": 0.266}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 161}, {"index": 27, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 67}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.153852, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 25, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 5.478, "width_percent": 2.511}, {"sql": "select `_lft`, `_rgt` from `categories` where `id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 235}, {"index": 18, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 227}, {"index": 19, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}], "start": **********.156696, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:38", "source": {"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=38", "ajax": false, "filename": "QueryBuilder.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 7.989, "width_percent": 0.178}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 14 and 27)", "type": "query", "params": [], "bindings": [1, 14, 27], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 17, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 169}, {"index": 18, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 67}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.157689, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "mlk", "explain": null, "start_percent": 8.166, "width_percent": 0.241}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10, 17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 169}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1587632, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "mlk", "explain": null, "start_percent": 8.407, "width_percent": 0.342}, {"sql": "select * from `categories` where `id` in (10)", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.160096, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:70", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=70", "ajax": false, "filename": "CategoryProductController.php", "line": "70"}, "connection": "mlk", "explain": null, "start_percent": 8.75, "width_percent": 0.279}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.161475, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:70", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=70", "ajax": false, "filename": "CategoryProductController.php", "line": "70"}, "connection": "mlk", "explain": null, "start_percent": 9.029, "width_percent": 0.228}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 290}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.162463, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 9.257, "width_percent": 0.178}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 290}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.163353, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 9.434, "width_percent": 0.228}, {"sql": "select `attributes`.*, `category_filterable_attributes`.`category_id` as `pivot_category_id`, `category_filterable_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `category_filterable_attributes` on `attributes`.`id` = `category_filterable_attributes`.`attribute_id` where `category_filterable_attributes`.`category_id` = 10", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 292}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.164325, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 9.663, "width_percent": 2.676}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (11, 23, 24, 25) order by `sort_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 292}, {"index": 27, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1674469, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 12.338, "width_percent": 0.304}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 495, 496, 497, 498)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 29, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 31, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 292}, {"index": 32, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.168725, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 29, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 12.643, "width_percent": 0.913}, {"sql": "select * from `attribute_translations` where `attribute_translations`.`attribute_id` in (11, 23, 24, 25)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 292}, {"index": 27, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.171226, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 13.556, "width_percent": 2.612}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 23 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, {"index": 17, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.17431, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:319", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=319", "ajax": false, "filename": "CategoryProductController.php", "line": "319"}, "connection": "mlk", "explain": null, "start_percent": 16.168, "width_percent": 0.266}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 4 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.179937, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 16.434, "width_percent": 0.266}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 495 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [495], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.183363, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 16.7, "width_percent": 0.507}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 496 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [496], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.186747, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.208, "width_percent": 0.254}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 497 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [497], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.189689, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.461, "width_percent": 0.228}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 498 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [498], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.192564, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.69, "width_percent": 0.228}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 1 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.195358, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 17.918, "width_percent": 0.254}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 2 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.198268, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 18.171, "width_percent": 0.254}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 3 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.201205, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 18.425, "width_percent": 0.241}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 5 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.204057, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 18.666, "width_percent": 0.216}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 24 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, {"index": 17, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.207823, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:319", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=319", "ajax": false, "filename": "CategoryProductController.php", "line": "319"}, "connection": "mlk", "explain": null, "start_percent": 18.882, "width_percent": 0.254}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 6 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.209786, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 19.135, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 7 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.2125418, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 19.351, "width_percent": 0.292}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 8 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.2153559, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 19.642, "width_percent": 0.266}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 9 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.21815, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 19.909, "width_percent": 0.228}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, {"index": 17, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 73}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2218099, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:319", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 319}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=319", "ajax": false, "filename": "CategoryProductController.php", "line": "319"}, "connection": "mlk", "explain": null, "start_percent": 20.137, "width_percent": 0.266}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 371 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [371], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.223779, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 20.403, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 372 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [372], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.226495, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 20.619, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 373 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [373], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.229285, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 20.834, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 374 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [374], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.232042, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.05, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 375 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [375], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.234783, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.266, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 376 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [376], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.237488, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.481, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 377 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [377], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.240187, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.697, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 378 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [378], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.242965, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.912, "width_percent": 0.254}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 379 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [379], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.2458, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 22.166, "width_percent": 0.216}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` = 380 and `attribute_option_translations`.`attribute_option_id` is not null", "type": "query", "params": [], "bindings": [380], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 326}], "start": **********.248533, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 22.381, "width_percent": 0.216}, {"sql": "select * from `categories` where `id` in (10)", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 395}, {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.252259, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:395", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=395", "ajax": false, "filename": "CategoryProductController.php", "line": "395"}, "connection": "mlk", "explain": null, "start_percent": 22.597, "width_percent": 0.241}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 395}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 76}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.253339, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "CategoryProductController.php:395", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 395}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=395", "ajax": false, "filename": "CategoryProductController.php", "line": "395"}, "connection": "mlk", "explain": null, "start_percent": 22.838, "width_percent": 0.228}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Category/src/Models/Category.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Category\\src\\Models\\Category.php", "line": 140}, {"index": 24, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 195}, {"index": 25, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}], "start": **********.257704, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 23.066, "width_percent": 0.203}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.266676, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 23.269, "width_percent": 2.777}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('category_ids', 'page', 'limit', 'sort', 'order', 'category_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["category_ids", "page", "limit", "sort", "order", "category_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 292}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.2701468, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 26.046, "width_percent": 0.279}, {"sql": "select * from `attributes` where `code` = 'price'", "type": "query", "params": [], "bindings": ["price"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 383}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.273211, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 26.325, "width_percent": 0.241}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_categories` on `product_categories`.`product_id` = `products`.`id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_categories`.`category_id` in ('10', '17', '18', '19', '20', '21', '22') and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, "10", "17", "18", "19", "20", "21", "22", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.27439, "duration": 0.01231, "duration_str": "12.31ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 26.566, "width_percent": 15.61}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 left join `product_categories` on `product_categories`.`product_id` = `products`.`id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` where `product_categories`.`category_id` in ('10', '17', '18', '19', '20', '21', '22') and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `product_price_indices`.`min_price` desc limit 6 offset 0", "type": "query", "params": [], "bindings": [1, "10", "17", "18", "19", "20", "21", "22", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.287687, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 42.176, "width_percent": 1.674}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.2901618, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 43.85, "width_percent": 2.485}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.2931619, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 46.335, "width_percent": 2.536}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.296234, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 48.871, "width_percent": 2.447}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.299084, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 51.319, "width_percent": 0.837}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.3022518, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 52.156, "width_percent": 0.33}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.303605, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 52.485, "width_percent": 2.65}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.306778, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 55.136, "width_percent": 2.397}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 423}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 213}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 96}], "start": **********.309493, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "mlk", "explain": null, "start_percent": 57.532, "width_percent": 0.203}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3109539, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 57.735, "width_percent": 0.292}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.312098, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 58.027, "width_percent": 0.9}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3149, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 58.927, "width_percent": 2.283}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.317544, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 61.21, "width_percent": 0.241}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3185098, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 61.451, "width_percent": 0.304}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1 and `rule_date` = '2025-08-05'", "type": "query", "params": [], "bindings": [1, 1, "2025-08-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.319829, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 61.755, "width_percent": 2.321}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `customer_group_id` = 1 order by `qty` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.322651, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 64.076, "width_percent": 2.333}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.325296, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 66.409, "width_percent": 2.206}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 27, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.328034, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 68.615, "width_percent": 2.244}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.330682, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 70.86, "width_percent": 0.228}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.331567, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 71.088, "width_percent": 0.203}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 99}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.332411, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "AbstractPaginator.php:790", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pagination/AbstractPaginator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 790}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2FAbstractPaginator.php&line=790", "ajax": false, "filename": "AbstractPaginator.php", "line": "790"}, "connection": "mlk", "explain": null, "start_percent": 71.291, "width_percent": 0.19}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.333298, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 71.481, "width_percent": 0.241}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.334283, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 71.722, "width_percent": 0.875}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3369749, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 72.597, "width_percent": 0.317}, {"sql": "select * from `products` where `products`.`parent_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.337994, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 72.914, "width_percent": 0.241}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.339028, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 73.155, "width_percent": 0.304}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `channel_id` = 1 and `customer_group_id` = 1 and `rule_date` = '2025-08-05'", "type": "query", "params": [], "bindings": [1, 1, "2025-08-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.340188, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 73.459, "width_percent": 0.254}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` in (132, 133, 136, 137, 138, 139) and `customer_group_id` = 1 order by `qty` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.341108, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 73.713, "width_percent": 0.241}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.342049, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 73.954, "width_percent": 0.279}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 26, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.343128, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 24, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 74.233, "width_percent": 0.241}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (132, 133, 136, 137, 138, 139)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.34411, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 74.474, "width_percent": 0.203}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (132, 133, 136, 137, 138, 139) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.344972, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 74.677, "width_percent": 0.216}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 171}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.345844, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "BaseProductFormatter.php:122", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FBaseProductFormatter.php&line=122", "ajax": false, "filename": "BaseProductFormatter.php", "line": "122"}, "connection": "mlk", "explain": null, "start_percent": 74.892, "width_percent": 0.165}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 153}], "start": **********.351789, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 75.057, "width_percent": 4.362}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 472}, {"index": 25, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 531}], "start": **********.3626132, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 79.419, "width_percent": 2.371}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.365369, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 81.791, "width_percent": 2.194}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.383639, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 83.984, "width_percent": 0.406}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.3847198, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 84.39, "width_percent": 0.254}, {"sql": "select * from `attributes` where `attributes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.385607, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 84.644, "width_percent": 0.266}, {"sql": "select * from `attributes` where `attributes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.386584, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 84.91, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.3873801, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.138, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.388133, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.316, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.388871, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.493, "width_percent": 0.304}, {"sql": "select * from `attributes` where `attributes`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.389763, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 85.798, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.390599, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.001, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.391434, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.191, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.392313, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.381, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.393066, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.584, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.393835, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.787, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.4073539, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 86.977, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 175}, {"index": 28, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/BaseProductFormatter.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\BaseProductFormatter.php", "line": 174}, {"index": 29, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CategoryProductController.php", "line": 102}], "start": **********.408163, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductResource.php:404", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Resources/ProductResource.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Resources\\ProductResource.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FResources%2FProductResource.php&line=404", "ajax": false, "filename": "ProductResource.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 87.205, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408906, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.395, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409182, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.573, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409454, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.75, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409723, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 87.928, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410021, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.131, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410284, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.308, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4105341, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.473, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410783, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.638, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411029, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.803, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4112751, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.968, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411521, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.133, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4242978, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.297, "width_percent": 0.292}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4247239, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.589, "width_percent": 0.241}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425093, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 89.83, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425411, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.02, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425708, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.21, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4259691, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.375, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426222, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.54, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426486, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.718, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426737, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.883, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426989, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.047, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4272392, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.212, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427488, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.377, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4277341, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.542, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4405398, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.707, "width_percent": 0.279}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44094, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 91.986, "width_percent": 0.279}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441302, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.265, "width_percent": 0.216}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441617, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.48, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4419, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.671, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442181, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.835, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442477, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.026, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442742, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.19, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4429932, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.355, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4432461, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.52, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443519, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.71, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443785, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 93.875, "width_percent": 0.254}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44414, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.129, "width_percent": 0.254}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.456732, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.382, "width_percent": 0.241}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4570642, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.623, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.457348, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.814, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.457602, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.978, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4578562, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.143, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458107, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.308, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458355, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.473, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458602, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.638, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458857, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 95.803, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459188, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.006, "width_percent": 0.368}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459633, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.373, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4599771, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.602, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460264, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.792, "width_percent": 0.165}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4728909, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.957, "width_percent": 0.304}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473298, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.261, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473627, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.489, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473947, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 97.717, "width_percent": 0.355}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.474373, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.073, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4747, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.301, "width_percent": 0.228}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.475046, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.529, "width_percent": 0.203}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4753401, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.732, "width_percent": 0.178}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.475607, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.909, "width_percent": 0.241}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4759321, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 99.15, "width_percent": 0.19}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4762099, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 99.341, "width_percent": 0.241}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.476546, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 99.582, "width_percent": 0.216}, {"sql": "select * from `attributes` where `attributes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.476844, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 99.797, "width_percent": 0.203}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 666, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOptionTranslation": {"value": 240, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOptionTranslation.php&line=1", "ajax": false, "filename": "AttributeOptionTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 114, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Category\\Models\\CategoryTranslation": {"value": 65, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Category\\Models\\Category": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeTranslation": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeTranslation.php&line=1", "ajax": false, "filename": "AttributeTranslation.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 1248, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/category/by-categories", "action_name": "mlk.api.category.products.by_categories", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CategoryProductController@getProductsByCategories", "uri": "POST api/mlk/category/by-categories", "controller": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CategoryProductController@getProductsByCategories<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=47\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/mlk/category", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryProductController.php&line=47\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CategoryProductController.php:47-147</a>", "middleware": "api_locale, api_auth", "duration": "573ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-829117917 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-829117917\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-761418644 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category_ids</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>10</span>\n  </samp>]\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>limit</span>\" => <span class=sf-dump-num>6</span>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761418644\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1954854144 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">69</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954854144\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-891228183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891228183\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-656939935 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:02:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656939935\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1812671279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1812671279\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/category/by-categories", "action_name": "mlk.api.category.products.by_categories", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CategoryProductController@getProductsByCategories"}, "badge": null}}