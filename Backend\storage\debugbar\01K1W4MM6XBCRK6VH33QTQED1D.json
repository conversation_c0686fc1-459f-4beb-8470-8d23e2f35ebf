{"__meta": {"id": "01K1W4MM6XBCRK6VH33QTQED1D", "datetime": "2025-08-05 04:33:13", "utime": **********.053832, "method": "GET", "uri": "/cache/original/product/219/iu5Amxao7zkYv7w7VdXTwjvRUPZnLIWOTVRvp1iZ.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754364792.853889, "end": **********.062914, "duration": 0.20902490615844727, "duration_str": "209ms", "measures": [{"label": "Booting", "start": 1754364792.853889, "relative_start": 0, "end": **********.037074, "relative_end": **********.037074, "duration": 0.*****************, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.037084, "relative_start": 0.****************, "end": **********.062915, "relative_end": 1.1920928955078125e-06, "duration": 0.025830984115600586, "duration_str": "25.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.047202, "relative_start": 0.****************, "end": **********.050342, "relative_end": **********.050342, "duration": 0.003139972686767578, "duration_str": "3.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.052472, "relative_start": 0.*****************, "end": **********.052568, "relative_end": **********.052568, "duration": 9.584426879882812e-05, "duration_str": "96μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.052578, "relative_start": 0.*****************, "end": **********.052589, "relative_end": **********.052589, "duration": 1.0967254638671875e-05, "duration_str": "11μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/219/iu5Amxao7zkYv7w7VdXTwjvRUPZnLIWOTVRvp1iZ.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "209ms", "peak_memory": "42MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-984165051 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-984165051\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2100705025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2100705025\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1811413831 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImhqRDd0WC9PUklSWDF4bUFyK0xOM0E9PSIsInZhbHVlIjoieVczYndHcWxiMFQ3MUVieGFTeWhNOEI1NnVtdW5VQ1orL0plNEo1RGpZM2RjcmM3ZzEzOElCNmRSZE0xaUU1ZkpKaitNOFYrZFZOaksyOEdzRml2cUhmLytoajNSdVhQR3hTUE5GNE1SS0JNM1lYbTNxRFVwVW1yOE1sYUxobzkiLCJtYWMiOiI0OGJlMjRjNGQwNDRhNWUwYTM3YTA5MGNlNjZjNmJiNGY2MThiMmRkMGNjYTRkZTVhMjU4ZjFjYzA3ODliNjg4IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkxmckZEakNmVWszVkN1aEhzTFVkUWc9PSIsInZhbHVlIjoiMll4NlRiazJTTnBxcGsrc1p1RG5GNVdubVRtNlpqa0ZLM09GR0NjYUNEWUtXYXVtK1RHbDVzVXVTQVVodG9CSWVLZXBIUUNvUHFycUJBRkF6RmNRRTFuaHpqL3V6K0ZnS1BvdzYvQVIyY1dDWmVZK2p6R0poTFNmOUwwUk41UjQiLCJtYWMiOiIzODQ3YTAyM2I1NjA4YWFjMzczZTBiMDNkOWY1NmY2YmM5OTRiZmM3MTliMzc3MWVlYjY2NzA1M2VhNzM5MDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://mlk.test/4125-iphone-ip17-pro</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811413831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889591662 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhqRDd0WC9PUklSWDF4bUFyK0xOM0E9PSIsInZhbHVlIjoieVczYndHcWxiMFQ3MUVieGFTeWhNOEI1NnVtdW5VQ1orL0plNEo1RGpZM2RjcmM3ZzEzOElCNmRSZE0xaUU1ZkpKaitNOFYrZFZOaksyOEdzRml2cUhmLytoajNSdVhQR3hTUE5GNE1SS0JNM1lYbTNxRFVwVW1yOE1sYUxobzkiLCJtYWMiOiI0OGJlMjRjNGQwNDRhNWUwYTM3YTA5MGNlNjZjNmJiNGY2MThiMmRkMGNjYTRkZTVhMjU4ZjFjYzA3ODliNjg4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxmckZEakNmVWszVkN1aEhzTFVkUWc9PSIsInZhbHVlIjoiMll4NlRiazJTTnBxcGsrc1p1RG5GNVdubVRtNlpqa0ZLM09GR0NjYUNEWUtXYXVtK1RHbDVzVXVTQVVodG9CSWVLZXBIUUNvUHFycUJBRkF6RmNRRTFuaHpqL3V6K0ZnS1BvdzYvQVIyY1dDWmVZK2p6R0poTFNmOUwwUk41UjQiLCJtYWMiOiIzODQ3YTAyM2I1NjA4YWFjMzczZTBiMDNkOWY1NmY2YmM5OTRiZmM3MTliMzc3MWVlYjY2NzA1M2VhNzM5MDJkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889591662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">61658</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">9b3e63a15c22dc00c91b2797a4171744</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:33:13 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1240187883 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1240187883\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/219/iu5Amxao7zkYv7w7VdXTwjvRUPZnLIWOTVRvp1iZ.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}