<?php

/**
 * 测试 group_device_variants 功能的脚本
 * 用于验证相同 group_code 产品的 brand、device、color 关联查询
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Attribute\Repositories\AttributeRepository;

// 模拟测试数据结构
echo "=== Group Device Variants 功能测试 ===\n\n";

// 1. 测试属性ID获取功能
echo "1. 测试属性ID获取:\n";
$attributeCodes = ['group_code', 'brand', 'device', 'color'];
echo "需要查询的属性: " . implode(', ', $attributeCodes) . "\n";

// 模拟查询结果
$mockAttributeIds = [
    'group_code' => 101,
    'brand' => 25,
    'device' => 33,
    'color' => 23
];
echo "模拟属性ID映射: " . json_encode($mockAttributeIds, JSON_PRETTY_PRINT) . "\n\n";

// 2. 测试SQL查询构建
echo "2. 生成的SQL查询结构:\n";
$sqlTemplate = "
SELECT 
    p.id as product_id,
    brand_val.integer_value as brand,
    device_val.text_value as device,
    color_val.integer_value as color
FROM products as p
JOIN product_attribute_values as group_val 
    ON p.id = group_val.product_id 
    AND group_val.attribute_id = :group_code_attr_id
LEFT JOIN product_attribute_values as brand_val 
    ON p.id = brand_val.product_id 
    AND brand_val.attribute_id = :brand_attr_id
LEFT JOIN product_attribute_values as device_val 
    ON p.id = device_val.product_id 
    AND device_val.attribute_id = :device_attr_id
LEFT JOIN product_attribute_values as color_val 
    ON p.id = color_val.product_id 
    AND color_val.attribute_id = :color_attr_id
WHERE group_val.text_value = :group_code_value
AND EXISTS (
    SELECT 1 FROM product_attribute_values as status_val
    JOIN attributes as status_attr ON status_val.attribute_id = status_attr.id
    WHERE status_val.product_id = p.id
    AND status_attr.code = 'status'
    AND status_val.boolean_value = 1
)
";

echo $sqlTemplate . "\n";

// 3. 模拟查询结果
echo "3. 模拟查询结果:\n";
$mockResults = [
    [
        'product_id' => 1001,
        'brand' => 371,      // Apple
        'device' => '381,382', // iPhone 16 Pro Max, iPhone 16 Pro
        'color' => 1         // Red
    ],
    [
        'product_id' => 1002,
        'brand' => 371,      // Apple
        'device' => '381',   // iPhone 16 Pro Max
        'color' => 2         // Green
    ],
    [
        'product_id' => 1003,
        'brand' => 372,      // Samsung
        'device' => '383',   // Galaxy S24
        'color' => 4         // Black
    ],
    [
        'product_id' => 1004,
        'brand' => 372,      // Samsung
        'device' => '383',   // Galaxy S24
        'color' => 5         // White
    ]
];

echo json_encode($mockResults, JSON_PRETTY_PRINT) . "\n\n";

// 4. 测试数据格式化
echo "4. 格式化后的返回数据:\n";
$formattedResults = array_map(function ($item) {
    return [
        'product_id' => $item['product_id'],
        'brand' => $item['brand'],
        'device' => $item['device'],
        'color' => $item['color'],
    ];
}, $mockResults);

echo json_encode($formattedResults, JSON_PRETTY_PRINT) . "\n\n";

// 5. 性能优化说明
echo "5. 性能优化特点:\n";
echo "✓ 使用单次JOIN查询，避免N+1问题\n";
echo "✓ 属性ID缓存，减少重复查询\n";
echo "✓ 只查询必要字段，减少数据传输\n";
echo "✓ 使用EXISTS子查询过滤有效产品\n";
echo "✓ 支持多语言和多渠道（可扩展）\n\n";

// 6. 使用示例
echo "6. API返回数据结构示例:\n";
$apiResponse = [
    'product' => [
        'id' => 1000,
        'name' => '示例产品',
        'group_code' => 'GROUP_001'
    ],
    'group_device_variants' => $formattedResults
];

echo json_encode($apiResponse, JSON_PRETTY_PRINT) . "\n\n";

echo "=== 测试完成 ===\n";
