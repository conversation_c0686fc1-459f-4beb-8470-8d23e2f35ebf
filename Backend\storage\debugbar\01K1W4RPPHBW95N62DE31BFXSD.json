{"__meta": {"id": "01K1W4RPPHBW95N62DE31BFXSD", "datetime": "2025-08-05 04:35:26", "utime": **********.673787, "method": "GET", "uri": "/cache/original/product/197/LrSi61dfo3Ieqdj94vdU0hHGeGyw6GwJWmG7MImE.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.448316, "end": **********.684445, "duration": 0.2361288070678711, "duration_str": "236ms", "measures": [{"label": "Booting", "start": **********.448316, "relative_start": 0, "end": **********.651331, "relative_end": **********.651331, "duration": 0.*****************, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.651342, "relative_start": 0.*****************, "end": **********.684447, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "33.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.663295, "relative_start": 0.*****************, "end": **********.667192, "relative_end": **********.667192, "duration": 0.003896951675415039, "duration_str": "3.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.672191, "relative_start": 0.*****************, "end": **********.67232, "relative_end": **********.67232, "duration": 0.0001289844512939453, "duration_str": "129μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.672335, "relative_start": 0.*****************, "end": **********.672348, "relative_end": **********.672348, "duration": 1.3113021850585938e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/197/LrSi61dfo3Ieqdj94vdU0hHGeGyw6GwJWmG7MImE.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "237ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1428707164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1428707164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1309028746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1309028746\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703115655 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkFENXZCU2FPbDdQbms4dnFmMGtVV2c9PSIsInZhbHVlIjoiTVFSOHBac0RBeVF0R2hRQnlmSit5TUtGZUtGVmRFVzkzeGNIWFhRaWQwUUJ6Q2NvVk45SERmbVpTM1FGZUsvUzNRVUhaaENwNWxhQXdDNlBtMUxPVnJZcHRtL1RpWXVyOThNRjZvcUpxcm1vM1h2aWFRSzNVWnVxYllaOEUvbWsiLCJtYWMiOiI3NDE4NjdiMTE3NjlmNGMzZjhlMjJlMzAxYzJiYjYzNDI4YzEyYTRiYzA0ZTZkZDdjODZkZGNjOWM0OWMyMjkxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkZtUzNYY09oRFlQbUJoUmFKWHkyZ3c9PSIsInZhbHVlIjoiL3RDZ295Q3E0TWplN2Z6eHRwbmZ5eGR5dEFoNjZIZFRhUzgxVVBlRUFGVHFwTmkxMTR4TXMxZEZGTVZrTVFsdkw2N3lGSnBQNEpmb0pPUUxmQm9IeEJlc1FoNGFzeGlheEpqS3N4TllUWEFKa3BaQ3FHNnBVOFU4NXFjaU5rUTIiLCJtYWMiOiJlYTM5YWY4MTc5MjlhNzk5MWYyYjcwNmMwZTliMGIyMzQ4MDljY2IzOTMwYzU4YmM1MzNiMzlkZDIyMWQ3ZTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://mlk.test/1022-iphone-ip16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703115655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429890473 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkFENXZCU2FPbDdQbms4dnFmMGtVV2c9PSIsInZhbHVlIjoiTVFSOHBac0RBeVF0R2hRQnlmSit5TUtGZUtGVmRFVzkzeGNIWFhRaWQwUUJ6Q2NvVk45SERmbVpTM1FGZUsvUzNRVUhaaENwNWxhQXdDNlBtMUxPVnJZcHRtL1RpWXVyOThNRjZvcUpxcm1vM1h2aWFRSzNVWnVxYllaOEUvbWsiLCJtYWMiOiI3NDE4NjdiMTE3NjlmNGMzZjhlMjJlMzAxYzJiYjYzNDI4YzEyYTRiYzA0ZTZkZDdjODZkZGNjOWM0OWMyMjkxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZtUzNYY09oRFlQbUJoUmFKWHkyZ3c9PSIsInZhbHVlIjoiL3RDZ295Q3E0TWplN2Z6eHRwbmZ5eGR5dEFoNjZIZFRhUzgxVVBlRUFGVHFwTmkxMTR4TXMxZEZGTVZrTVFsdkw2N3lGSnBQNEpmb0pPUUxmQm9IeEJlc1FoNGFzeGlheEpqS3N4TllUWEFKa3BaQ3FHNnBVOFU4NXFjaU5rUTIiLCJtYWMiOiJlYTM5YWY4MTc5MjlhNzk5MWYyYjcwNmMwZTliMGIyMzQ4MDljY2IzOTMwYzU4YmM1MzNiMzlkZDIyMWQ3ZTc0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429890473\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-464642219 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">39646</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">68aca413e802d153ac94e94ac6d5cd72</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 05 Aug 2025 03:35:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464642219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-539968466 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-539968466\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/original/product/197/LrSi61dfo3Ieqdj94vdU0hHGeGyw6GwJWmG7MImE.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}